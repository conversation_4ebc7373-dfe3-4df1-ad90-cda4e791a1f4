# 📘 System Requirements Specification (SRS)

**Project Title**: Intelligent Data Unification System  
**Goal**: Uniquely unify related data records (e.g., entities like pharmacy names and addresses) even when they appear in different forms or languages.

---

## 1. Overview

The system aims to detect and unify entities that refer to the same real-world object but are represented differently. This includes handling variations in:
- Spelling (e.g., typos or abbreviations)
- Language (e.g., Arabic vs. English transliteration)
- Structure (e.g., order of words or missing fields)
- Format (e.g., different address styles)

---

## 2. Functional Requirements

### 2.1 Data Ingestion
- Accept records from multiple sources (CSV, databases, APIs)
- Support batch and real-time ingestion modes
- Ingested fields may include:
  - Name (e.g., pharmacy, doctor, clinic)
  - Address (street, city, governorate, country)
  - Phone numbers
  - Licenses or identifiers

### 2.2 Preprocessing
- Normalize text (e.g., remove diacritics, unify letter forms, standardize punctuation)
- Transliterate Arabic ↔ English when needed
- Tokenize text into comparable chunks
- Remove stopwords and common phrases (e.g., "صيدلية", "Pharmacy")

### 2.3 Similarity Matching Engine
- Use multiple similarity strategies:
  - Fuzzy string matching (<PERSON><PERSON><PERSON><PERSON>, <PERSON>aro<PERSON><PERSON>)
  - Phonetic matching (Soundex, Metaphone)
  - Vector-based semantic similarity (e.g., multilingual BERT, FastText)
- Weighted scoring across fields (e.g., name + address)
- Entity-type specific logic (pharmacy, doctor, etc.)

### 2.4 Unified Entity Resolution
- Cluster matching records into unified entities
- Assign canonical representation
- Track all source/original records and metadata

### 2.5 Manual Review Interface *(Optional)*
- Admin dashboard to:
  - View match suggestions + confidence
  - Approve/reject/merge records manually

### 2.6 Feedback Loop *(Optional)*
- Use user feedback to refine and improve future matches

---

## 3. Non-Functional Requirements

### 3.1 Performance
- Support unifying millions of records efficiently
- Chunked processing for large datasets with low memory usage

### 3.2 Accuracy
- Aim for >90% precision in high-confidence matches
- Provide adjustable confidence thresholds

### 3.3 Scalability
- Horizontal scaling via containers (Docker, Kubernetes)

### 3.4 Localization
- Full support for Arabic and English
- Unicode-safe text processing

### 3.5 Logging & Auditing
- Log all unified/matched operations
- Maintain audit trail for changes and decisions

---

## 4. Technology Recommendations

| Component   | Recommendation                         |
|-------------|-----------------------------------------|
| Language    | Node.js / Python                        |
| NLP         | FastText, BERT (multilingual), fuzzywuzzy, rapidfuzz |
| Vector DB   | Qdrant, Weaviate, or Pinecone           |
| Database    | PostgreSQL / MongoDB                    |
| Interface   | Vue.js / React                          |
| Deployment  | Docker, Kubernetes                      |
| Logging     | ELK Stack or Loki + Grafana             |

---

## 5. Example Scenario

### Input Records:
```json
{
  "name": "Ahmed Mohamed Abd Elsalam",
  "address": "Rosseta, El Behera, Egypt"
}
{
  "name": "احمد محمد عبد السلام",
  "address": "رشيد البحيره مصر"
}
```

### Unified Output:
```json
{
  "unified_id": "pharmacy_237a",
  "canonical_name": "Ahmed Mohamed Abd Elsalam",
  "aliases": [
    "احمد محمد عبد السلام"
  ],
  "canonical_address": "Rosetta, El Beheira, Egypt",
  "original_records": [...]
}
```

---
