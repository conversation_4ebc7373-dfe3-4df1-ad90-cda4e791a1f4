{"name": "intelligent-data-unification-system", "version": "1.0.0", "description": "Intelligent Data Unification System for multilingual entity matching", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest tests/integration", "test:unit": "jest tests/unit", "test:verbose": "jest --verbose", "docker:build": "docker build -t data-unification .", "docker:run": "docker run -p 3000:3000 data-unification", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "mongoose": "^7.5.0", "pg": "^8.11.3", "csv-parser": "^3.0.0", "multer": "^1.4.5-lts.1", "natural": "^6.6.0", "fuzzball": "^2.1.2", "transliteration": "^2.3.5", "node-nlp": "^4.27.0", "winston": "^3.10.0", "bull": "^4.11.3", "redis": "^4.6.8", "ioredis": "^5.3.2", "uuid": "^9.0.0", "moment": "^2.29.4", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-jest": "^29.6.4", "eslint": "^8.45.0", "eslint-config-node": "^4.1.0", "mongodb-memory-server": "^8.13.0"}}