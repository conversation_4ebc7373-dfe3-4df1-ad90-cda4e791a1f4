
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.75% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>31/400</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/198</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.66% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/60</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>31/359</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="DataIngestionService.js"><a href="DataIngestionService.js.html">DataIngestionService.js</a></td>
	<td data-value="7.86" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.86" class="pct low">7.86%</td>
	<td data-value="89" class="abs low">7/89</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="41" class="abs low">0/41</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="13" class="abs low">1/13</td>
	<td data-value="8.13" class="pct low">8.13%</td>
	<td data-value="86" class="abs low">7/86</td>
	</tr>

<tr>
	<td class="file low" data-value="PreprocessingService.js"><a href="PreprocessingService.js.html">PreprocessingService.js</a></td>
	<td data-value="11.47" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.47" class="pct low">11.47%</td>
	<td data-value="61" class="abs low">7/61</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="11" class="abs low">1/11</td>
	<td data-value="13.2" class="pct low">13.2%</td>
	<td data-value="53" class="abs low">7/53</td>
	</tr>

<tr>
	<td class="file low" data-value="SimilarityService.js"><a href="SimilarityService.js.html">SimilarityService.js</a></td>
	<td data-value="9.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.3" class="pct low">9.3%</td>
	<td data-value="86" class="abs low">8/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="14" class="abs low">1/14</td>
	<td data-value="10.66" class="pct low">10.66%</td>
	<td data-value="75" class="abs low">8/75</td>
	</tr>

<tr>
	<td class="file low" data-value="UnificationService.js"><a href="UnificationService.js.html">UnificationService.js</a></td>
	<td data-value="5.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.48" class="pct low">5.48%</td>
	<td data-value="164" class="abs low">9/164</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="4.54" class="pct low">4.54%</td>
	<td data-value="22" class="abs low">1/22</td>
	<td data-value="6.2" class="pct low">6.2%</td>
	<td data-value="145" class="abs low">9/145</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T08:08:31.126Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    