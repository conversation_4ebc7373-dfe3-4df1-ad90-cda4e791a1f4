
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/jobs/processors</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/jobs/processors</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.89% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>50/562</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/131</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/39</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">9.09% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>50/550</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="batchProcessors.js"><a href="batchProcessors.js.html">batchProcessors.js</a></td>
	<td data-value="5.04" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.04" class="pct low">5.04%</td>
	<td data-value="119" class="abs low">6/119</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="57" class="abs low">0/57</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="5.08" class="pct low">5.08%</td>
	<td data-value="118" class="abs low">6/118</td>
	</tr>

<tr>
	<td class="file low" data-value="cleanupProcessors.js"><a href="cleanupProcessors.js.html">cleanupProcessors.js</a></td>
	<td data-value="7.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.09" class="pct low">7.09%</td>
	<td data-value="141" class="abs low">10/141</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="7.19" class="pct low">7.19%</td>
	<td data-value="139" class="abs low">10/139</td>
	</tr>

<tr>
	<td class="file low" data-value="dataIngestionProcessors.js"><a href="dataIngestionProcessors.js.html">dataIngestionProcessors.js</a></td>
	<td data-value="19.6" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 19%"></div><div class="cover-empty" style="width: 81%"></div></div>
	</td>
	<td data-value="19.6" class="pct low">19.6%</td>
	<td data-value="51" class="abs low">10/51</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="19.6" class="pct low">19.6%</td>
	<td data-value="51" class="abs low">10/51</td>
	</tr>

<tr>
	<td class="file low" data-value="preprocessingProcessors.js"><a href="preprocessingProcessors.js.html">preprocessingProcessors.js</a></td>
	<td data-value="11.11" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="72" class="abs low">8/72</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="11.26" class="pct low">11.26%</td>
	<td data-value="71" class="abs low">8/71</td>
	</tr>

<tr>
	<td class="file low" data-value="similarityProcessors.js"><a href="similarityProcessors.js.html">similarityProcessors.js</a></td>
	<td data-value="9.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.3" class="pct low">9.3%</td>
	<td data-value="86" class="abs low">8/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="9.87" class="pct low">9.87%</td>
	<td data-value="81" class="abs low">8/81</td>
	</tr>

<tr>
	<td class="file low" data-value="unificationProcessors.js"><a href="unificationProcessors.js.html">unificationProcessors.js</a></td>
	<td data-value="8.6" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.6" class="pct low">8.6%</td>
	<td data-value="93" class="abs low">8/93</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="8.88" class="pct low">8.88%</td>
	<td data-value="90" class="abs low">8/90</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T08:08:31.126Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    