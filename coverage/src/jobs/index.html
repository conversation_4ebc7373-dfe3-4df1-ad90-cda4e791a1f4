
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/jobs</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/jobs</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.95% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>61/142</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.16% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.95% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>61/142</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="jobQueue.js"><a href="jobQueue.js.html">jobQueue.js</a></td>
	<td data-value="69.11" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.11" class="pct medium">69.11%</td>
	<td data-value="68" class="abs medium">47/68</td>
	<td data-value="51.85" class="pct medium">51.85%</td>
	<td data-value="27" class="abs medium">14/27</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="7" class="abs high">6/7</td>
	<td data-value="69.11" class="pct medium">69.11%</td>
	<td data-value="68" class="abs medium">47/68</td>
	</tr>

<tr>
	<td class="file low" data-value="jobSetup.js"><a href="jobSetup.js.html">jobSetup.js</a></td>
	<td data-value="18.91" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.91" class="pct low">18.91%</td>
	<td data-value="74" class="abs low">14/74</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="18.91" class="pct low">18.91%</td>
	<td data-value="74" class="abs low">14/74</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T08:08:31.126Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    