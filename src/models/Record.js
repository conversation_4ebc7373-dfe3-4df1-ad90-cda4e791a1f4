const mongoose = require('mongoose');

const recordSchema = new mongoose.Schema({
  sourceId: { type: String, required: true },
  source: { type: String, required: true },
  entityType: {
    type: String,
    required: true,
    enum: ['pharmacy', 'doctor', 'clinic', 'hospital', 'other']
  },
  originalData: {
    name: { type: String, required: true },
    address: String,
    phone: String,
    license: String,
    metadata: mongoose.Schema.Types.Mixed
  },
  normalizedData: {
    name: String,
    nameTokens: [String],
    address: String,
    addressTokens: [String],
    phone: String,
    transliterations: {
      nameArabic: String,
      nameEnglish: String,
      addressArabic: String,
      addressEnglish: String
    },
    languages: {
      name: String,
      address: String
    },
    entityType: String
  },
  potentialMatches: [{
    recordId: { type: mongoose.Schema.Types.ObjectId, ref: 'Record' },
    score: { type: Number, min: 0, max: 1 },
    confidence: { type: Number, min: 0, max: 1 },
    matchedFields: [String],
    breakdown: mongoose.Schema.Types.Mixed
  }],
  unifiedEntityId: { type: String, index: true },
  processingStatus: {
    type: String,
    enum: [
      'pending',
      'normalized',
      'matched',
      'unified',
      'failed',
      'match_failed',
      'unification_failed',
      'processed' // legacy status
    ],
    default: 'pending'
  },
  processingError: String,
  autoProcess: { type: Boolean, default: true },
  confidence: { type: Number, min: 0, max: 1 },
  auditTrail: [{
    action: String,
    timestamp: { type: Date, default: Date.now },
    userId: String,
    details: mongoose.Schema.Types.Mixed
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for performance
recordSchema.index({ 'normalizedData.name': 'text', 'normalizedData.address': 'text' });
recordSchema.index({ source: 1, sourceId: 1 }, { unique: true });
recordSchema.index({ processingStatus: 1 });
recordSchema.index({ entityType: 1 });
recordSchema.index({ unifiedEntityId: 1 });
recordSchema.index({ createdAt: -1 });
recordSchema.index({ updatedAt: -1 });

// Pre-save middleware to update timestamps
recordSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
recordSchema.methods.addAuditEntry = function(action, userId, details) {
  this.auditTrail.push({
    action,
    userId,
    details,
    timestamp: new Date()
  });
};

recordSchema.methods.updateProcessingStatus = function(status, error = null) {
  this.processingStatus = status;
  if (error) {
    this.processingError = error;
  } else {
    this.processingError = undefined;
  }
  this.updatedAt = new Date();
};

// Static methods
recordSchema.statics.findByProcessingStatus = function(status) {
  return this.find({ processingStatus: status });
};

recordSchema.statics.findPendingProcessing = function() {
  return this.find({
    processingStatus: { $in: ['pending', 'failed'] },
    autoProcess: true
  });
};

recordSchema.statics.getProcessingStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$processingStatus',
        count: { $sum: 1 }
      }
    }
  ]);
};

module.exports = mongoose.model('Record', recordSchema);