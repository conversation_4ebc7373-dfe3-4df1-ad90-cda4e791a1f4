const mongoose = require('mongoose');

const unifiedEntitySchema = new mongoose.Schema({
  unifiedId: { type: String, required: true, unique: true },
  entityType: {
    type: String,
    required: true,
    enum: ['pharmacy', 'doctor', 'clinic', 'hospital', 'other']
  },
  canonicalData: {
    name: { type: String, required: true },
    address: String,
    phone: String,
    license: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },
  aliases: [{
    name: String,
    language: { type: String, enum: ['ar', 'en', 'mixed', 'unknown'] },
    source: String,
    confidence: Number
  }],
  sourceRecords: [{
    recordId: { type: mongoose.Schema.Types.ObjectId, ref: 'Record' },
    confidence: Number,
    isCanonical: { type: Boolean, default: false },
    addedAt: { type: Date, default: Date.now }
  }],
  statistics: {
    totalRecords: { type: Number, default: 0 },
    averageConfidence: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now },
    unificationMethod: String,
    processingTime: Number
  },
  reviewStatus: {
    type: String,
    enum: ['auto_unified', 'pending_review', 'approved', 'rejected', 'needs_revision'],
    default: 'auto_unified'
  },
  requiresManualReview: { type: Boolean, default: false },
  reviewReason: String,
  reviewHistory: [{
    action: String,
    reviewerId: String,
    timestamp: { type: Date, default: Date.now },
    reason: String,
    previousStatus: String,
    newStatus: String
  }],
  auditTrail: [{
    action: String,
    timestamp: { type: Date, default: Date.now },
    userId: String,
    details: mongoose.Schema.Types.Mixed
  }],
  tags: [String],
  metadata: mongoose.Schema.Types.Mixed,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for performance
unifiedEntitySchema.index({ unifiedId: 1 });
unifiedEntitySchema.index({ 'canonicalData.name': 'text', 'canonicalData.address': 'text' });
unifiedEntitySchema.index({ entityType: 1 });
unifiedEntitySchema.index({ reviewStatus: 1 });
unifiedEntitySchema.index({ requiresManualReview: 1 });
unifiedEntitySchema.index({ createdAt: -1 });
unifiedEntitySchema.index({ updatedAt: -1 });
unifiedEntitySchema.index({ 'statistics.averageConfidence': -1 });

// Pre-save middleware to update timestamps and statistics
unifiedEntitySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  this.statistics.lastUpdated = new Date();
  this.statistics.totalRecords = this.sourceRecords.length;

  if (this.sourceRecords.length > 0) {
    const totalConfidence = this.sourceRecords.reduce((sum, record) => sum + (record.confidence || 0), 0);
    this.statistics.averageConfidence = totalConfidence / this.sourceRecords.length;
  }

  next();
});

// Instance methods
unifiedEntitySchema.methods.addAuditEntry = function(action, userId, details) {
  this.auditTrail.push({
    action,
    userId,
    details,
    timestamp: new Date()
  });
};

unifiedEntitySchema.methods.addReviewEntry = function(action, reviewerId, reason, previousStatus, newStatus) {
  this.reviewHistory.push({
    action,
    reviewerId,
    reason,
    previousStatus,
    newStatus,
    timestamp: new Date()
  });
};

unifiedEntitySchema.methods.updateReviewStatus = function(status, reason, reviewerId) {
  const previousStatus = this.reviewStatus;
  this.reviewStatus = status;
  this.reviewReason = reason;

  this.addReviewEntry('status_change', reviewerId, reason, previousStatus, status);
  this.addAuditEntry('review_status_updated', reviewerId, {
    previousStatus,
    newStatus: status,
    reason
  });
};

unifiedEntitySchema.methods.addSourceRecord = function(recordId, confidence, isCanonical = false) {
  this.sourceRecords.push({
    recordId,
    confidence,
    isCanonical,
    addedAt: new Date()
  });

  this.addAuditEntry('source_record_added', null, {
    recordId,
    confidence,
    isCanonical
  });
};

// Static methods
unifiedEntitySchema.statics.findByReviewStatus = function(status) {
  return this.find({ reviewStatus: status });
};

unifiedEntitySchema.statics.findRequiringReview = function() {
  return this.find({ requiresManualReview: true });
};

unifiedEntitySchema.statics.getUnificationStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$reviewStatus',
        count: { $sum: 1 },
        avgConfidence: { $avg: '$statistics.averageConfidence' },
        totalRecords: { $sum: '$statistics.totalRecords' }
      }
    }
  ]);
};

unifiedEntitySchema.statics.findSimilarEntities = function(name, entityType, limit = 10) {
  return this.find({
    entityType,
    $text: { $search: name }
  }, {
    score: { $meta: 'textScore' }
  })
  .sort({ score: { $meta: 'textScore' } })
  .limit(limit);
};

module.exports = mongoose.model('UnifiedEntity', unifiedEntitySchema);