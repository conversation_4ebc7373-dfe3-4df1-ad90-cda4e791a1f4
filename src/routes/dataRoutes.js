const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const DataIngestionService = require('../services/DataIngestionService.js');
const Record = require('../models/Record.js');
const logger = require('../utils/logger.js');

const router = express.Router();
const dataIngestionService = new DataIngestionService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = './uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.csv', '.json'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV and JSON files are allowed'));
    }
  }
});

// Upload and ingest CSV/JSON file
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { source = 'file_upload', entityType = 'pharmacy', skipDuplicates = 'true' } = req.body;
    const filePath = req.file.path;
    const fileExt = path.extname(req.file.originalname).toLowerCase();

    let results;
    
    if (fileExt === '.csv') {
      results = await dataIngestionService.ingestFromCSV(filePath, {
        source,
        entityType,
        skipDuplicates: skipDuplicates === 'true'
      });
    } else if (fileExt === '.json') {
      const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      results = await dataIngestionService.ingestFromJSON(jsonData, {
        source,
        entityType,
        skipDuplicates: skipDuplicates === 'true'
      });
    }

    // Clean up uploaded file
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: 'File processed successfully',
      results
    });

  } catch (error) {
    logger.error('File upload error:', error);
    
    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ error: error.message });
  }
});

// Ingest single record via API
router.post('/ingest', async (req, res) => {
  try {
    const { source, entityType, autoProcess, ...recordData } = req.body;

    if (!recordData.name) {
      return res.status(400).json({ error: 'Name field is required' });
    }

    const record = await dataIngestionService.ingestRecord(recordData, {
      source: source || 'api',
      entityType: entityType || 'pharmacy',
      autoProcess: autoProcess !== false
    });

    res.json({
      success: true,
      message: 'Record ingested successfully',
      recordId: record._id
    });

  } catch (error) {
    logger.error('API ingestion error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get records with filtering and pagination
router.get('/records', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      source,
      entityType,
      processingStatus,
      search
    } = req.query;

    const query = {};
    
    if (source) query.source = source;
    if (entityType) query.entityType = entityType;
    if (processingStatus) query.processingStatus = processingStatus;
    
    if (search) {
      query.$or = [
        { 'originalData.name': new RegExp(search, 'i') },
        { 'originalData.address': new RegExp(search, 'i') },
        { 'normalizedData.name': new RegExp(search, 'i') }
      ];
    }

    const total = await Record.countDocuments(query);
    const records = await Record.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    res.json({
      records,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Get records error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get single record by ID
router.get('/records/:id', async (req, res) => {
  try {
    const record = await Record.findById(req.params.id);
    
    if (!record) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json(record);

  } catch (error) {
    logger.error('Get record error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update record
router.put('/records/:id', async (req, res) => {
  try {
    const updates = req.body;
    delete updates._id; // Prevent ID updates

    const record = await Record.findByIdAndUpdate(
      req.params.id,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!record) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json({
      success: true,
      message: 'Record updated successfully',
      record
    });

  } catch (error) {
    logger.error('Update record error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete record
router.delete('/records/:id', async (req, res) => {
  try {
    const record = await Record.findByIdAndDelete(req.params.id);

    if (!record) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json({
      success: true,
      message: 'Record deleted successfully'
    });

  } catch (error) {
    logger.error('Delete record error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;