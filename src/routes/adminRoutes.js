const express = require('express');
const Record = require('../models/Record.js');
const UnifiedEntity = require('../models/UnifiedEntity.js');
const { getJobQueue, getJobStats } = require('../jobs/jobQueue.js');
const logger = require('../utils/logger.js');

const router = express.Router();

// Get dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    const [
      totalRecords,
      processedRecords,
      pendingRecords,
      totalEntities,
      pendingReview,
      approvedEntities,
      rejectedEntities
    ] = await Promise.all([
      Record.countDocuments(),
      Record.countDocuments({ processingStatus: 'processed' }),
      Record.countDocuments({ processingStatus: 'pending' }),
      UnifiedEntity.countDocuments(),
      UnifiedEntity.countDocuments({ reviewStatus: 'pending_review' }),
      UnifiedEntity.countDocuments({ reviewStatus: 'approved' }),
      UnifiedEntity.countDocuments({ reviewStatus: 'rejected' })
    ]);

    // Get confidence distribution
    const confidenceDistribution = await UnifiedEntity.aggregate([
      {
        $bucket: {
          groupBy: '$statistics.averageConfidence',
          boundaries: [0, 0.5, 0.7, 0.8, 0.9, 1.0],
          default: 'other',
          output: { count: { $sum: 1 } }
        }
      }
    ]);

    // Get entity type distribution
    const entityTypeDistribution = await Record.aggregate([
      { $group: { _id: '$entityType', count: { $sum: 1 } } }
    ]);

    // Get source distribution
    const sourceDistribution = await Record.aggregate([
      { $group: { _id: '$source', count: { $sum: 1 } } }
    ]);

    // Get recent activity
    const recentEntities = await UnifiedEntity.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .select('unifiedId canonicalData.name statistics createdAt reviewStatus');

    const stats = {
      overview: {
        totalRecords,
        processedRecords,
        pendingRecords,
        totalEntities,
        processingRate: totalRecords > 0 ? (processedRecords / totalRecords) * 100 : 0,
        compressionRatio: processedRecords > 0 ? totalEntities / processedRecords : 0
      },
      review: {
        pendingReview,
        approvedEntities,
        rejectedEntities,
        reviewRate: totalEntities > 0 ? ((approvedEntities + rejectedEntities) / totalEntities) * 100 : 0
      },
      distributions: {
        confidence: confidenceDistribution,
        entityType: entityTypeDistribution,
        source: sourceDistribution
      },
      recentActivity: recentEntities
    };

    res.json(stats);

  } catch (error) {
    logger.error('Dashboard stats error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get job queue status
router.get('/jobs', async (req, res) => {
  try {
    const stats = await getJobStats();
    res.json(stats);

  } catch (error) {
    logger.error('Job stats error:', error);
    res.status(500).json({ error: error.message });
  }
});


router.get('/health', async (req, res) => {
    // TODO: NOT IMPLEMENTED YET

});

module.exports = router;
