const express = require('express');
const UnificationService = require('../services/UnificationService.js');
const UnifiedEntity = require('../models/UnifiedEntity.js');
const Record = require('../models/Record.js');
const { addJob } = require('../jobs/jobQueue.js');
const logger = require('../utils/logger.js');

const router = express.Router();
const unificationService = new UnificationService();

// Start unification process
router.post('/start', async (req, res) => {
  try {
    const {
      entityType,
      source,
      batchSize = 100,
      confidenceThreshold = 0.75,
      enableManualReview = true,
      asyncMode = true
    } = req.body;

    const query = {};
    if (entityType) query.entityType = entityType;
    if (source) query.source = source;
    query.processingStatus = 'pending';

    const recordCount = await Record.countDocuments(query);
    
    if (recordCount === 0) {
      return res.json({
        success: false,
        message: 'No pending records found for unification'
      });
    }

    if (asyncMode) {
      // Queue the unification job
      const job = await addJob('unification', {
        query,
        options: {
          batchSize,
          confidenceThreshold,
          enableManualReview
        }
      });

      res.json({
        success: true,
        message: 'Unification process started',
        jobId: job.id,
        estimatedRecords: recordCount,
        mode: 'async'
      });
    } else {
      // Synchronous processing for smaller datasets
      const records = await Record.find(query).limit(1000);
      
      const results = await unificationService.unifyRecords(records, {
        batchSize,
        confidenceThreshold,
        enableManualReview
      });

      res.json({
        success: true,
        message: 'Unification completed',
        results,
        mode: 'sync'
      });
    }

  } catch (error) {
    logger.error('Unification start error:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

// Get unification statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await unificationService.getUnificationStats();
    res.json(stats);

  } catch (error) {
    logger.error('Get stats error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get unified entities with filtering and pagination
router.get('/entities', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      entityType,
      reviewStatus,
      search,
      minConfidence,
      maxConfidence
    } = req.query;

    const query = {};
    
    if (entityType) query.entityType = entityType;
    if (reviewStatus) query.reviewStatus = reviewStatus;
    
    if (minConfidence || maxConfidence) {
      query['statistics.averageConfidence'] = {};
      if (minConfidence) query['statistics.averageConfidence'].$gte = parseFloat(minConfidence);
      if (maxConfidence) query['statistics.averageConfidence'].$lte = parseFloat(maxConfidence);
    }
    
    if (search) {
      query.$or = [
        { 'canonicalData.name': new RegExp(search, 'i') },
        { 'canonicalData.address': new RegExp(search, 'i') },
        { 'aliases.name': new RegExp(search, 'i') }
      ];
    }

    const total = await UnifiedEntity.countDocuments(query);
    const entities = await UnifiedEntity.find(query)
      .populate('sourceRecords.recordId', 'originalData source createdAt')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ 'statistics.lastUpdated': -1 });

    res.json({
      entities,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Get entities error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get single unified entity
router.get('/entities/:id', async (req, res) => {
  try {
    const entity = await UnifiedEntity.findById(req.params.id)
      .populate('sourceRecords.recordId', 'originalData source createdAt normalizedData');

    if (!entity) {
      return res.status(404).json({ error: 'Unified entity not found' });
    }

    res.json(entity);

  } catch (error) {
    logger.error('Get entity error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update unified entity (manual review)
router.put('/entities/:id', async (req, res) => {
  try {
    const updates = req.body;
    delete updates._id;
    delete updates.unifiedId; // Prevent ID changes

    const entity = await UnifiedEntity.findByIdAndUpdate(
      req.params.id,
      { 
        ...updates, 
        updatedAt: new Date(),
        'statistics.lastUpdated': new Date()
      },
      { new: true, runValidators: true }
    );

    if (!entity) {
      return res.status(404).json({ error: 'Unified entity not found' });
    }

    res.json({
      success: true,
      message: 'Entity updated successfully',
      entity
    });

  } catch (error) {
    logger.error('Update entity error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Approve/reject entity (manual review)
router.post('/entities/:id/review', async (req, res) => {
  try {
    const { action, reason } = req.body; // action: 'approve' | 'reject'
    
    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({ error: 'Invalid action. Use "approve" or "reject"' });
    }

    const entity = await UnifiedEntity.findByIdAndUpdate(
      req.params.id,
      {
        reviewStatus: action === 'approve' ? 'approved' : 'rejected',
        reviewReason: reason,
        reviewedAt: new Date(),
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!entity) {
      return res.status(404).json({ error: 'Unified entity not found' });
    }

    res.json({
      success: true,
      message: `Entity ${action}d successfully`,
      entity
    });

  } catch (error) {
    logger.error('Review entity error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Merge two entities manually
router.post('/entities/:id/merge/:targetId', async (req, res) => {
  try {
    const sourceEntity = await UnifiedEntity.findById(req.params.id)
      .populate('sourceRecords.recordId');
    const targetEntity = await UnifiedEntity.findById(req.params.targetId)
      .populate('sourceRecords.recordId');

    if (!sourceEntity || !targetEntity) {
      return res.status(404).json({ error: 'One or both entities not found' });
    }

    // Merge source records
    const allRecords = [
      ...targetEntity.sourceRecords,
      ...sourceEntity.sourceRecords.map(sr => ({
        ...sr,
        isCanonical: false // Only target entity keeps canonical status
      }))
    ];

    // Merge aliases
    const allAliases = [
      ...targetEntity.aliases,
      ...sourceEntity.aliases,
      {
        name: sourceEntity.canonicalData.name,
        language: 'mixed',
        source: 'merged_entity'
      }
    ];

    // Update target entity
    await UnifiedEntity.findByIdAndUpdate(req.params.targetId, {
      sourceRecords: allRecords,
      aliases: allAliases,
      'statistics.totalRecords': allRecords.length,
      'statistics.averageConfidence': (
        targetEntity.statistics.averageConfidence + sourceEntity.statistics.averageConfidence
      ) / 2,
      'statistics.lastUpdated': new Date(),
      updatedAt: new Date()
    });

    // Update all source records to point to target entity
    await Record.updateMany(
      { unifiedEntityId: sourceEntity.unifiedId },
      { unifiedEntityId: targetEntity.unifiedId }
    );

    // Delete source entity
    await UnifiedEntity.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Entities merged successfully',
      targetEntityId: req.params.targetId
    });

  } catch (error) {
    logger.error('Merge entities error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Split entity (remove records from unified entity)
router.post('/entities/:id/split', async (req, res) => {
  try {
    const { recordIds } = req.body;
    
    if (!recordIds || !Array.isArray(recordIds)) {
      return res.status(400).json({ error: 'recordIds array is required' });
    }

    const entity = await UnifiedEntity.findById(req.params.id);
    if (!entity) {
      return res.status(404).json({ error: 'Entity not found' });
    }

    // Remove specified records from entity
    const remainingRecords = entity.sourceRecords.filter(
      sr => !recordIds.includes(sr.recordId.toString())
    );

    if (remainingRecords.length === 0) {
      // Delete entity if no records remain
      await UnifiedEntity.findByIdAndDelete(req.params.id);
      await Record.updateMany(
        { _id: { $in: recordIds } },
        { $unset: { unifiedEntityId: 1 }, processingStatus: 'pending' }
      );
    } else {
      // Update entity with remaining records
      await UnifiedEntity.findByIdAndUpdate(req.params.id, {
        sourceRecords: remainingRecords,
        'statistics.totalRecords': remainingRecords.length,
        'statistics.lastUpdated': new Date(),
        updatedAt: new Date()
      });

      // Reset split records to pending
      await Record.updateMany(
        { _id: { $in: recordIds } },
        { $unset: { unifiedEntityId: 1 }, processingStatus: 'pending' }
      );
    }

    res.json({
      success: true,
      message: 'Entity split successfully',
      removedRecords: recordIds.length
    });

  } catch (error) {
    logger.error('Split entity error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;