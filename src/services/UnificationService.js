const { v4: uuidv4 } = require('uuid');
const Record = require('../models/Record.js');
const UnifiedEntity = require('../models/UnifiedEntity.js');
const SimilarityService = require('./SimilarityService.js');
const logger = require('../utils/logger.js');

class UnificationService {
  constructor() {
    this.similarityService = new SimilarityService();
    this.confidenceThreshold = 0.75;
    this.reviewThreshold = 0.85;
  }

  // Main unification process
  async unifyRecords(records, options = {}) {
    const {
      batchSize = 100,
      confidenceThreshold = this.confidenceThreshold,
      enableManualReview = true
    } = options;

    const results = {
      processed: 0,
      unified: 0,
      needsReview: 0,
      errors: 0,
      entities: []
    };

    try {
      // Process records in batches
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        const batchResults = await this.processBatch(batch, confidenceThreshold, enableManualReview);
        
        results.processed += batchResults.processed;
        results.unified += batchResults.unified;
        results.needsReview += batchResults.needsReview;
        results.errors += batchResults.errors;
        results.entities.push(...batchResults.entities);

        logger.info(`Processed batch ${Math.floor(i/batchSize) + 1}, total processed: ${results.processed}`);
      }

      return results;
    } catch (error) {
      logger.error('Unification process error:', error);
      throw error;
    }
  }

  // Process a batch of records
  async processBatch(records, confidenceThreshold, enableManualReview) {
    const results = {
      processed: 0,
      unified: 0,
      needsReview: 0,
      errors: 0,
      entities: []
    };

    // Build similarity matrix
    const similarityMatrix = await this.buildSimilarityMatrix(records, confidenceThreshold);
    
    // Cluster similar records
    const clusters = this.clusterRecords(similarityMatrix, records);
    
    // Create unified entities from clusters
    for (const cluster of clusters) {
      try {
        const entity = await this.createUnifiedEntity(cluster, enableManualReview);
        results.entities.push(entity);
        results.unified++;
        
        if (entity.reviewStatus === 'pending_review') {
          results.needsReview++;
        }
      } catch (error) {
        logger.error('Error creating unified entity:', error);
        results.errors++;
      }
    }

    results.processed = records.length;
    return results;
  }

  // Build similarity matrix for clustering
  async buildSimilarityMatrix(records, threshold) {
    const matrix = [];
    
    for (let i = 0; i < records.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < records.length; j++) {
        if (i === j) {
          matrix[i][j] = { similarity: 1.0, confidence: 1.0 };
        } else if (j < i) {
          // Use symmetry
          matrix[i][j] = matrix[j][i];
        } else {
          const result = this.similarityService.calculateSimilarity(records[i], records[j]);
          matrix[i][j] = {
            similarity: result.overall,
            confidence: this.similarityService.calculateConfidence(result.overall, result.breakdown),
            breakdown: result.breakdown
          };
        }
      }
    }
    
    return matrix;
  }

  // Cluster records using similarity scores
  clusterRecords(similarityMatrix, records) {
    const clusters = [];
    const visited = new Set();
    const threshold = this.confidenceThreshold;

    for (let i = 0; i < records.length; i++) {
      if (visited.has(i)) continue;

      const cluster = [i];
      visited.add(i);

      // Find all records similar to the current one
      for (let j = i + 1; j < records.length; j++) {
        if (visited.has(j)) continue;

        if (similarityMatrix[i][j].confidence >= threshold) {
          cluster.push(j);
          visited.add(j);
        }
      }

      // Add records to cluster based on transitive similarity
      let changed = true;
      while (changed) {
        changed = false;
        for (let j = 0; j < records.length; j++) {
          if (visited.has(j)) continue;

          // Check if j is similar to any record in the cluster
          for (const clusterIndex of cluster) {
            if (similarityMatrix[Math.min(clusterIndex, j)][Math.max(clusterIndex, j)].confidence >= threshold) {
              cluster.push(j);
              visited.add(j);
              changed = true;
              break;
            }
          }
        }
      }

      clusters.push({
        indices: cluster,
        records: cluster.map(idx => records[idx]),
        averageConfidence: this.calculateClusterConfidence(cluster, similarityMatrix)
      });
    }

    return clusters;
  }

  // Calculate average confidence for a cluster
  calculateClusterConfidence(cluster, similarityMatrix) {
    if (cluster.length <= 1) return 1.0;

    let totalConfidence = 0;
    let pairCount = 0;

    for (let i = 0; i < cluster.length; i++) {
      for (let j = i + 1; j < cluster.length; j++) {
        const idx1 = cluster[i];
        const idx2 = cluster[j];
        totalConfidence += similarityMatrix[Math.min(idx1, idx2)][Math.max(idx1, idx2)].confidence;
        pairCount++;
      }
    }

    return pairCount > 0 ? totalConfidence / pairCount : 1.0;
  }

  // Create unified entity from cluster
  async createUnifiedEntity(cluster, enableManualReview) {
    const unifiedId = uuidv4();
    const canonicalRecord = this.selectCanonicalRecord(cluster.records);
    
    // Determine review status
    const reviewStatus = enableManualReview && cluster.averageConfidence < this.reviewThreshold
      ? 'pending_review'
      : 'auto_unified';

    // Create aliases from all records
    const aliases = cluster.records
      .filter(record => record._id.toString() !== canonicalRecord._id.toString())
      .map(record => ({
        name: record.originalData.name,
        language: this.detectLanguage(record.originalData.name),
        source: record.source
      }));

    // Create unified entity
    const unifiedEntity = new UnifiedEntity({
      unifiedId,
      entityType: canonicalRecord.entityType,
      canonicalData: {
        name: canonicalRecord.originalData.name,
        address: canonicalRecord.originalData.address,
        phone: canonicalRecord.originalData.phone
      },
      aliases,
      sourceRecords: cluster.records.map(record => ({
        recordId: record._id,
        confidence: cluster.averageConfidence,
        isCanonical: record._id.toString() === canonicalRecord._id.toString()
      })),
      statistics: {
        totalRecords: cluster.records.length,
        averageConfidence: cluster.averageConfidence,
        lastUpdated: new Date()
      },
      reviewStatus
    });

    await unifiedEntity.save();

    // Update source records
    await Record.updateMany(
      { _id: { $in: cluster.records.map(r => r._id) } },
      { 
        unifiedEntityId: unifiedId,
        processingStatus: 'processed',
        confidence: cluster.averageConfidence,
        updatedAt: new Date()
      }
    );

    return unifiedEntity;
  }

  // Select the best record as canonical
  selectCanonicalRecord(records) {
    // Prefer records with more complete information
    return records.reduce((best, current) => {
      const bestScore = this.calculateCompletenessScore(best);
      const currentScore = this.calculateCompletenessScore(current);
      return currentScore > bestScore ? current : best;
    });
  }

  // Calculate completeness score for record selection
  calculateCompletenessScore(record) {
    let score = 0;
    
    if (record.originalData.name) score += 3;
    if (record.originalData.address) score += 2;
    if (record.originalData.phone) score += 2;
    if (record.originalData.license) score += 1;
    
    // Prefer English names for canonical representation
    if (this.detectLanguage(record.originalData.name) === 'en') score += 1;
    
    return score;
  }

  // Simple language detection
  detectLanguage(text) {
    if (!text) return 'unknown';
    const arabicRegex = /[\u0600-\u06FF]/;
    return arabicRegex.test(text) ? 'ar' : 'en';
  }

  // Unify a specific set of records (used by job processors)
  async unifyRecords(records, entityType) {
    const startTime = Date.now();

    try {
      if (!records || records.length === 0) {
        throw new Error('No records provided for unification');
      }

      // If only one record, create a unified entity with just that record
      if (records.length === 1) {
        return await this.createSingleRecordEntity(records[0], entityType);
      }

      // For multiple records, create a cluster and unify
      const cluster = {
        records,
        averageConfidence: this.calculateAverageConfidence(records),
        indices: records.map((_, index) => index)
      };

      const unifiedEntity = await this.createUnifiedEntity(cluster, true);

      const processingTime = Date.now() - startTime;

      logger.audit.entityUnification('UNIFY_RECORDS_SERVICE_COMPLETED', {
        unifiedEntityId: unifiedEntity._id,
        sourceRecords: records.map(r => r._id),
        canonicalData: unifiedEntity.canonicalData,
        confidence: cluster.averageConfidence,
        manualReview: unifiedEntity.requiresManualReview,
      });

      return unifiedEntity;
    } catch (error) {
      logger.error('Unify records error:', error);
      throw error;
    }
  }

  // Create unified entity for a single record
  async createSingleRecordEntity(record, entityType) {
    const unifiedId = uuidv4();

    const unifiedEntity = new UnifiedEntity({
      unifiedId,
      entityType,
      canonicalData: {
        name: record.originalData.name,
        address: record.originalData.address,
        phone: record.originalData.phone
      },
      aliases: [],
      sourceRecords: [{
        recordId: record._id,
        confidence: 1.0,
        isCanonical: true
      }],
      statistics: {
        totalRecords: 1,
        averageConfidence: 1.0,
        lastUpdated: new Date()
      },
      reviewStatus: 'auto_unified',
      requiresManualReview: false
    });

    await unifiedEntity.save();
    return unifiedEntity;
  }

  // Calculate average confidence for a set of records
  calculateAverageConfidence(records) {
    if (records.length <= 1) return 1.0;

    // If records have potential matches, use those confidence scores
    let totalConfidence = 0;
    let pairCount = 0;

    for (const record of records) {
      if (record.potentialMatches && record.potentialMatches.length > 0) {
        for (const match of record.potentialMatches) {
          totalConfidence += match.confidence;
          pairCount++;
        }
      }
    }

    return pairCount > 0 ? totalConfidence / pairCount : 0.8; // Default confidence
  }

  // Get unification statistics
  async getUnificationStats() {
    const totalRecords = await Record.countDocuments();
    const processedRecords = await Record.countDocuments({ processingStatus: 'processed' });
    const totalEntities = await UnifiedEntity.countDocuments();
    const pendingReview = await UnifiedEntity.countDocuments({ reviewStatus: 'pending_review' });

    const avgConfidence = await UnifiedEntity.aggregate([
      { $group: { _id: null, avgConfidence: { $avg: '$statistics.averageConfidence' } } }
    ]);

    return {
      totalRecords,
      processedRecords,
      totalEntities,
      pendingReview,
      processingRate: totalRecords > 0 ? (processedRecords / totalRecords) * 100 : 0,
      averageConfidence: avgConfidence[0]?.avgConfidence || 0,
      compressionRatio: processedRecords > 0 ? totalEntities / processedRecords : 0
    };
  }
}

module.exports = UnificationService;