const csv = require('csv-parser');
const fs = require('fs');
const Record = require('../models/Record.js');
const PreprocessingService = require('./PreprocessingService.js');
const logger = require('../utils/logger.js');

class DataIngestionService {
  constructor() {
    this.preprocessingService = new PreprocessingService();
  }

  // Ingest data from CSV file
  async ingestFromCSV(filePath, options = {}) {
    const {
      source = 'csv_upload',
      entityType = 'pharmacy',
      batchSize = 1000,
      skipDuplicates = true
    } = options;

    const results = {
      recordCount: 0,
      recordIds: [],
      skipped: 0,
      errors: []
    };

    return new Promise((resolve, reject) => {
      const records = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          records.push(row);

          // Process in batches
          if (records.length >= batchSize) {
            this.processBatch(records.splice(0, batchSize), source, entityType, skipDuplicates, results);
          }
        })
        .on('end', async () => {
          try {
            // Process remaining records
            if (records.length > 0) {
              await this.processBatch(records, source, entityType, skipDuplicates, results);
            }

            logger.audit.dataIngestion('CSV_INGESTION_SERVICE_COMPLETED', {
              recordId: null,
              source,
              entityType,
              recordCount: results.recordCount,
            });

            resolve(results);
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          logger.error('CSV ingestion error:', error);
          reject(error);
        });
    });
  }

  // Process batch of records
  async processBatch(batch, source, entityType, skipDuplicates, results) {
    for (const row of batch) {
      try {
        const recordData = this.mapCSVRow(row, entityType);

        // Check for duplicates if enabled
        if (skipDuplicates) {
          const existingRecord = await Record.findOne({
            source,
            sourceId: recordData.sourceId
          });

          if (existingRecord) {
            results.skipped++;
            continue;
          }
        }

        // Create record without preprocessing (will be done by job queue)
        const record = new Record({
          sourceId: recordData.sourceId,
          source,
          entityType,
          originalData: recordData.originalData,
          processingStatus: 'pending',
          autoProcess: true
        });

        await record.save();
        results.recordCount++;
        results.recordIds.push(record._id);

      } catch (error) {
        logger.error('Error processing record:', error);
        results.errors.push({
          row: JSON.stringify(row).substring(0, 200),
          error: error.message
        });
      }
    }
  }

  // Map CSV row to record structure
  mapCSVRow(row, entityType) {
    // Flexible mapping based on common column names
    const nameFields = ['name', 'pharmacy_name', 'doctor_name', 'clinic_name', 'title'];
    const addressFields = ['address', 'location', 'full_address', 'street'];
    const phoneFields = ['phone', 'telephone', 'mobile', 'contact'];
    const licenseFields = ['license', 'license_number', 'registration_id', 'id'];

    const findField = (fields) => {
      for (const field of fields) {
        const key = Object.keys(row).find(k => k.toLowerCase().includes(field.toLowerCase()));
        if (key && row[key]) return row[key];
      }
      return null;
    };

    const name = findField(nameFields);
    const address = findField(addressFields);
    const phone = findField(phoneFields);
    const license = findField(licenseFields);

    if (!name) {
      throw new Error('Name field is required but not found in CSV row');
    }

    return {
      sourceId: license || `${source}_${Date.now()}_${Math.random()}`,
      originalData: {
        name: name.trim(),
        address: address?.trim(),
        phone: phone?.trim(),
        license: license?.trim(),
        metadata: row // Store original row for reference
      }
    };
  }

  // Ingest from JSON array
  async ingestFromJSON(jsonData, options = {}) {
    const {
      source = 'json_upload',
      entityType = 'pharmacy',
      batchSize = 1000,
      skipDuplicates = true
    } = options;

    const results = {
      recordCount: 0,
      recordIds: [],
      skipped: 0,
      errors: []
    };

    try {
      const dataArray = Array.isArray(jsonData) ? jsonData : [jsonData];

      for (let i = 0; i < dataArray.length; i += batchSize) {
        const batch = dataArray.slice(i, i + batchSize);
        await this.processJSONBatch(batch, source, entityType, skipDuplicates, results);
      }

      logger.audit.dataIngestion('JSON_INGESTION_SERVICE_COMPLETED', {
        recordId: null,
        source,
        entityType,
        recordCount: results.recordCount,
      });

      return results;
    } catch (error) {
      logger.error('JSON ingestion error:', error);
      throw error;
    }
  }

  // Process JSON batch
  async processJSONBatch(batch, source, entityType, skipDuplicates, results) {
    for (const item of batch) {
      try {
        const sourceId = item.id || item.sourceId || `${source}_${Date.now()}_${Math.random()}`;

        if (skipDuplicates) {
          const existingRecord = await Record.findOne({ source, sourceId });
          if (existingRecord) {
            results.skipped++;
            continue;
          }
        }

        const record = new Record({
          sourceId,
          source,
          entityType,
          originalData: {
            name: item.name,
            address: item.address,
            phone: item.phone,
            license: item.license,
            metadata: item
          },
          processingStatus: 'pending',
          autoProcess: true
        });

        await record.save();
        results.recordCount++;
        results.recordIds.push(record._id);

      } catch (error) {
        logger.error('Error processing JSON record:', error);
        results.errors.push({
          item: JSON.stringify(item).substring(0, 200),
          error: error.message
        });
      }
    }
  }

  // Real-time ingestion from API
  async ingestRecord(recordData, options = {}) {
    const {
      source = 'api',
      entityType = 'pharmacy',
      autoProcess = true
    } = options;

    try {
      const record = new Record({
        sourceId: recordData.sourceId || `${source}_${Date.now()}`,
        source,
        entityType,
        originalData: recordData,
        processingStatus: 'pending',
        autoProcess
      });

      await record.save();

      logger.audit.dataIngestion('SINGLE_RECORD_INGESTION_SERVICE_COMPLETED', {
        recordId: record._id,
        source,
        entityType,
        recordCount: 1,
      });

      return record;

    } catch (error) {
      logger.error('Real-time ingestion error:', error);
      throw error;
    }
  }
}

module.exports = DataIngestionService;