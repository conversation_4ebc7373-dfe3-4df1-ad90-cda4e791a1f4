const { queues, JOB_TYPES } = require('./jobQueue');
const logger = require('../utils/logger');

// Import job processors
const dataIngestionProcessors = require('./processors/dataIngestionProcessors');
const preprocessingProcessors = require('./processors/preprocessingProcessors');
const similarityProcessors = require('./processors/similarityProcessors');
const unificationProcessors = require('./processors/unificationProcessors');
const batchProcessors = require('./processors/batchProcessors');
const cleanupProcessors = require('./processors/cleanupProcessors');

// Setup job processors for each queue
const setupJobProcessors = () => {
  try {
    // Data Ingestion Queue Processors
    queues.dataIngestion.process(JOB_TYPES.INGEST_CSV, 5, dataIngestionProcessors.processCSVIngestion);
    queues.dataIngestion.process(JOB_TYPES.INGEST_JSON, 5, dataIngestionProcessors.processJSONIngestion);
    queues.dataIngestion.process(JOB_TYPES.INGEST_RECORD, 10, dataIngestionProcessors.processSingleRecord);

    // Preprocessing Queue Processors
    queues.preprocessing.process(JOB_TYPES.NORMALIZE_RECORD, 10, preprocessingProcessors.processRecordNormalization);
    queues.preprocessing.process(JOB_TYPES.BATCH_NORMALIZE, 3, preprocessingProcessors.processBatchNormalization);

    // Similarity Matching Queue Processors
    queues.similarityMatching.process(JOB_TYPES.FIND_MATCHES, 5, similarityProcessors.processFindMatches);
    queues.similarityMatching.process(JOB_TYPES.BATCH_MATCH, 2, similarityProcessors.processBatchMatching);

    // Entity Unification Queue Processors
    queues.entityUnification.process(JOB_TYPES.UNIFY_ENTITIES, 3, unificationProcessors.processEntityUnification);
    queues.entityUnification.process(JOB_TYPES.BATCH_UNIFY, 1, unificationProcessors.processBatchUnification);

    // Batch Processing Queue Processors
    queues.batchProcessing.process(JOB_TYPES.PROCESS_DATASET, 1, batchProcessors.processDataset);
    queues.batchProcessing.process(JOB_TYPES.REPROCESS_FAILED, 2, batchProcessors.reprocessFailedJobs);

    // Cleanup Queue Processors
    queues.cleanup.process(JOB_TYPES.CLEANUP_TEMP_FILES, 1, cleanupProcessors.cleanupTempFiles);
    queues.cleanup.process(JOB_TYPES.ARCHIVE_OLD_LOGS, 1, cleanupProcessors.archiveOldLogs);
    queues.cleanup.process(JOB_TYPES.OPTIMIZE_DATABASE, 1, cleanupProcessors.optimizeDatabase);

    logger.info('Job processors setup completed');
  } catch (error) {
    logger.error('Failed to setup job processors', {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Setup global event handlers for all queues
const setupGlobalEventHandlers = () => {
  Object.entries(queues).forEach(([queueName, queue]) => {
    // Job completed successfully
    queue.on('completed', (job, result) => {
      logger.audit.jobProcessing('JOB_COMPLETED', {
        jobId: job.id,
        jobType: job.name,
        queueName,
        processingTime: job.finishedOn - job.processedOn,
        result: typeof result === 'object' ? JSON.stringify(result).substring(0, 500) : result,
      });
    });

    // Job failed
    queue.on('failed', (job, err) => {
      logger.audit.jobProcessing('JOB_FAILED', {
        jobId: job.id,
        jobType: job.name,
        queueName,
        error: err.message,
        stack: err.stack,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts,
      });
    });

    // Job started processing
    queue.on('active', (job) => {
      logger.audit.jobProcessing('JOB_STARTED', {
        jobId: job.id,
        jobType: job.name,
        queueName,
        attempts: job.attemptsMade + 1,
      });
    });

    // Job stalled (taking too long)
    queue.on('stalled', (job) => {
      logger.warn('Job stalled', {
        jobId: job.id,
        jobType: job.name,
        queueName,
        attempts: job.attemptsMade,
      });
    });

    // Job progress updated
    queue.on('progress', (job, progress) => {
      if (progress % 25 === 0) { // Log every 25% progress
        logger.debug('Job progress updated', {
          jobId: job.id,
          jobType: job.name,
          queueName,
          progress,
        });
      }
    });

    // Queue error
    queue.on('error', (error) => {
      logger.error('Queue error', {
        queueName,
        error: error.message,
        stack: error.stack,
      });
    });
  });

  logger.info('Global event handlers setup completed');
};

// Setup scheduled cleanup jobs
const setupScheduledJobs = async () => {
  try {
    // Schedule daily cleanup of temporary files
    await queues.cleanup.add(
      JOB_TYPES.CLEANUP_TEMP_FILES,
      { maxAge: 24 * 60 * 60 * 1000 }, // 24 hours
      {
        repeat: { cron: '0 2 * * *' }, // Daily at 2 AM
        removeOnComplete: 5,
        removeOnFail: 2,
      }
    );

    // Schedule weekly log archiving
    await queues.cleanup.add(
      JOB_TYPES.ARCHIVE_OLD_LOGS,
      { maxAge: 7 * 24 * 60 * 60 * 1000 }, // 7 days
      {
        repeat: { cron: '0 3 * * 0' }, // Weekly on Sunday at 3 AM
        removeOnComplete: 3,
        removeOnFail: 1,
      }
    );

    // Schedule monthly database optimization
    await queues.cleanup.add(
      JOB_TYPES.OPTIMIZE_DATABASE,
      {},
      {
        repeat: { cron: '0 4 1 * *' }, // Monthly on 1st at 4 AM
        removeOnComplete: 2,
        removeOnFail: 1,
      }
    );

    logger.info('Scheduled jobs setup completed');
  } catch (error) {
    logger.error('Failed to setup scheduled jobs', {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Graceful shutdown handler
const gracefulShutdown = async () => {
  logger.info('Starting graceful shutdown of job queues...');

  try {
    // Close all queues
    const closePromises = Object.entries(queues).map(async ([queueName, queue]) => {
      try {
        await queue.close();
        logger.info(`Queue '${queueName}' closed successfully`);
      } catch (error) {
        logger.error(`Failed to close queue '${queueName}'`, {
          error: error.message,
        });
      }
    });

    await Promise.all(closePromises);
    logger.info('All job queues closed successfully');
  } catch (error) {
    logger.error('Error during graceful shutdown', {
      error: error.message,
      stack: error.stack,
    });
  }
};

// Main setup function
const setupJobs = async () => {
  try {
    logger.info('Setting up job processing system...');

    setupJobProcessors();
    setupGlobalEventHandlers();
    await setupScheduledJobs();

    // Setup graceful shutdown
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    logger.info('Job processing system setup completed successfully');
  } catch (error) {
    logger.error('Failed to setup job processing system', {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  setupJobs,
  gracefulShutdown,
};