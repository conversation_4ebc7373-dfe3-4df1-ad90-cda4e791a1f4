const Queue = require('bull');
const { getRedis } = require('../config/redis');
const logger = require('../utils/logger');

// Job queue configurations
const queueConfig = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
  },
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50,      // Keep last 50 failed jobs
    attempts: 3,           // Retry failed jobs 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Create job queues for different types of processing
const queues = {
  // Data ingestion queue for processing uploaded files and API data
  dataIngestion: new Queue('data-ingestion', queueConfig),

  // Preprocessing queue for normalizing and cleaning data
  preprocessing: new Queue('preprocessing', queueConfig),

  // Similarity matching queue for finding potential matches
  similarityMatching: new Queue('similarity-matching', queueConfig),

  // Entity unification queue for merging matched records
  entityUnification: new Queue('entity-unification', queueConfig),

  // Batch processing queue for large dataset operations
  batchProcessing: new Queue('batch-processing', queueConfig),

  // Cleanup queue for maintenance tasks
  cleanup: new Queue('cleanup', queueConfig),
};

// Job types enum
const JOB_TYPES = {
  // Data ingestion jobs
  INGEST_CSV: 'ingest-csv',
  INGEST_JSON: 'ingest-json',
  INGEST_RECORD: 'ingest-record',

  // Preprocessing jobs
  NORMALIZE_RECORD: 'normalize-record',
  BATCH_NORMALIZE: 'batch-normalize',

  // Similarity matching jobs
  FIND_MATCHES: 'find-matches',
  BATCH_MATCH: 'batch-match',

  // Entity unification jobs
  UNIFY_ENTITIES: 'unify-entities',
  BATCH_UNIFY: 'batch-unify',

  // Batch processing jobs
  PROCESS_DATASET: 'process-dataset',
  REPROCESS_FAILED: 'reprocess-failed',

  // Cleanup jobs
  CLEANUP_TEMP_FILES: 'cleanup-temp-files',
  ARCHIVE_OLD_LOGS: 'archive-old-logs',
  OPTIMIZE_DATABASE: 'optimize-database',
};

// Job priority levels
const JOB_PRIORITY = {
  LOW: 1,
  NORMAL: 5,
  HIGH: 10,
  CRITICAL: 15,
};

// Queue management functions
const queueManager = {
  // Add job to appropriate queue
  addJob: async (queueName, jobType, data, options = {}) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      const jobOptions = {
        ...queueConfig.defaultJobOptions,
        ...options,
        priority: options.priority || JOB_PRIORITY.NORMAL,
      };

      const job = await queue.add(jobType, data, jobOptions);

      logger.audit.jobProcessing('JOB_ADDED', {
        jobId: job.id,
        jobType,
        queueName,
        priority: jobOptions.priority,
        data: JSON.stringify(data).substring(0, 500), // Truncate large data
      });

      return job;
    } catch (error) {
      logger.error('Failed to add job to queue', {
        queueName,
        jobType,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  },

  // Get job status
  getJobStatus: async (queueName, jobId) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      const job = await queue.getJob(jobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        type: job.name,
        data: job.data,
        progress: job.progress(),
        state: await job.getState(),
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : null,
        failedReason: job.failedReason,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts,
      };
    } catch (error) {
      logger.error('Failed to get job status', {
        queueName,
        jobId,
        error: error.message,
      });
      throw error;
    }
  },

  // Get queue statistics
  getQueueStats: async (queueName) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      return {
        queueName,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length,
      };
    } catch (error) {
      logger.error('Failed to get queue statistics', {
        queueName,
        error: error.message,
      });
      throw error;
    }
  },

  // Get all queue statistics
  getAllQueueStats: async () => {
    const stats = {};
    for (const queueName of Object.keys(queues)) {
      try {
        stats[queueName] = await queueManager.getQueueStats(queueName);
      } catch (error) {
        stats[queueName] = { error: error.message };
      }
    }
    return stats;
  },

  // Pause queue
  pauseQueue: async (queueName) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      await queue.pause();
      logger.info(`Queue '${queueName}' paused`);
    } catch (error) {
      logger.error('Failed to pause queue', {
        queueName,
        error: error.message,
      });
      throw error;
    }
  },

  // Resume queue
  resumeQueue: async (queueName) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      await queue.resume();
      logger.info(`Queue '${queueName}' resumed`);
    } catch (error) {
      logger.error('Failed to resume queue', {
        queueName,
        error: error.message,
      });
      throw error;
    }
  },

  // Clean queue (remove completed/failed jobs)
  cleanQueue: async (queueName, grace = 0, limit = 100) => {
    try {
      const queue = queues[queueName];
      if (!queue) {
        throw new Error(`Queue '${queueName}' not found`);
      }

      await queue.clean(grace, 'completed', limit);
      await queue.clean(grace, 'failed', limit);

      logger.info(`Queue '${queueName}' cleaned`, { grace, limit });
    } catch (error) {
      logger.error('Failed to clean queue', {
        queueName,
        error: error.message,
      });
      throw error;
    }
  },
};

module.exports = {
  queues,
  queueManager,
  JOB_TYPES,
  JOB_PRIORITY,
};