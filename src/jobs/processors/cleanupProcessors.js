const fs = require('fs').promises;
const path = require('path');
const logger = require('../../utils/logger');
const Record = require('../../models/Record');
const UnifiedEntity = require('../../models/UnifiedEntity');
const { pgPool } = require('../../config/database');

// Cleanup temporary files
const cleanupTempFiles = async (job) => {
  const { maxAge = 24 * 60 * 60 * 1000 } = job.data; // Default 24 hours
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('CLEANUP_TEMP_FILES_STARTED', {
      jobId: job.id,
      maxAge,
    });

    const results = {
      filesDeleted: 0,
      bytesFreed: 0,
      errors: [],
    };

    // Define directories to clean
    const tempDirectories = [
      path.join(process.cwd(), 'uploads'),
      path.join(process.cwd(), 'temp'),
      path.join(process.cwd(), 'cache'),
    ];

    const cutoffTime = Date.now() - maxAge;

    job.progress(10);

    // Clean each directory
    for (let i = 0; i < tempDirectories.length; i++) {
      const dir = tempDirectories[i];
      
      try {
        // Check if directory exists
        await fs.access(dir);
        
        // Read directory contents
        const files = await fs.readdir(dir, { withFileTypes: true });
        
        for (const file of files) {
          if (file.isFile()) {
            const filePath = path.join(dir, file.name);
            
            try {
              const stats = await fs.stat(filePath);
              
              // Delete files older than maxAge
              if (stats.mtime.getTime() < cutoffTime) {
                await fs.unlink(filePath);
                results.filesDeleted++;
                results.bytesFreed += stats.size;
                
                logger.debug('Deleted temporary file', {
                  filePath,
                  size: stats.size,
                  age: Date.now() - stats.mtime.getTime(),
                });
              }
            } catch (fileError) {
              results.errors.push({
                file: filePath,
                error: fileError.message,
              });
              
              logger.warn('Failed to process file during cleanup', {
                filePath,
                error: fileError.message,
              });
            }
          }
        }
      } catch (dirError) {
        if (dirError.code !== 'ENOENT') { // Ignore if directory doesn't exist
          results.errors.push({
            directory: dir,
            error: dirError.message,
          });
          
          logger.warn('Failed to access directory during cleanup', {
            directory: dir,
            error: dirError.message,
          });
        }
      }
      
      // Update progress
      const progress = 10 + Math.round(((i + 1) / tempDirectories.length) * 80);
      job.progress(progress);
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.jobProcessing('CLEANUP_TEMP_FILES_COMPLETED', {
      jobId: job.id,
      filesDeleted: results.filesDeleted,
      bytesFreed: results.bytesFreed,
      errors: results.errors.length,
      processingTime,
    });

    return {
      success: true,
      ...results,
      processingTime,
    };

  } catch (error) {
    logger.error('Cleanup temp files job failed', {
      jobId: job.id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Archive old log files
const archiveOldLogs = async (job) => {
  const { maxAge = 7 * 24 * 60 * 60 * 1000 } = job.data; // Default 7 days
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('ARCHIVE_OLD_LOGS_STARTED', {
      jobId: job.id,
      maxAge,
    });

    const results = {
      logsArchived: 0,
      bytesArchived: 0,
      errors: [],
    };

    const logsDir = path.join(process.cwd(), 'logs');
    const archiveDir = path.join(logsDir, 'archive');
    
    job.progress(10);

    try {
      // Ensure archive directory exists
      await fs.mkdir(archiveDir, { recursive: true });
      
      // Read logs directory
      const files = await fs.readdir(logsDir, { withFileTypes: true });
      const cutoffTime = Date.now() - maxAge;

      job.progress(20);

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.isFile() && file.name.endsWith('.log')) {
          const filePath = path.join(logsDir, file.name);
          
          try {
            const stats = await fs.stat(filePath);
            
            // Archive logs older than maxAge
            if (stats.mtime.getTime() < cutoffTime) {
              const timestamp = new Date().toISOString().split('T')[0];
              const archiveName = `${timestamp}_${file.name}`;
              const archivePath = path.join(archiveDir, archiveName);
              
              // Move file to archive
              await fs.rename(filePath, archivePath);
              
              results.logsArchived++;
              results.bytesArchived += stats.size;
              
              logger.debug('Archived log file', {
                originalPath: filePath,
                archivePath,
                size: stats.size,
                age: Date.now() - stats.mtime.getTime(),
              });
            }
          } catch (fileError) {
            results.errors.push({
              file: filePath,
              error: fileError.message,
            });
            
            logger.warn('Failed to archive log file', {
              filePath,
              error: fileError.message,
            });
          }
        }
        
        // Update progress
        const progress = 20 + Math.round(((i + 1) / files.length) * 70);
        job.progress(progress);
      }
    } catch (dirError) {
      results.errors.push({
        directory: logsDir,
        error: dirError.message,
      });
      
      logger.warn('Failed to access logs directory', {
        directory: logsDir,
        error: dirError.message,
      });
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.jobProcessing('ARCHIVE_OLD_LOGS_COMPLETED', {
      jobId: job.id,
      logsArchived: results.logsArchived,
      bytesArchived: results.bytesArchived,
      errors: results.errors.length,
      processingTime,
    });

    return {
      success: true,
      ...results,
      processingTime,
    };

  } catch (error) {
    logger.error('Archive old logs job failed', {
      jobId: job.id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Optimize database
const optimizeDatabase = async (job) => {
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('OPTIMIZE_DATABASE_STARTED', {
      jobId: job.id,
    });

    const results = {
      mongoOptimizations: [],
      pgOptimizations: [],
      errors: [],
    };

    job.progress(10);

    // MongoDB optimizations
    try {
      // Remove orphaned records (records without unified entities after a certain time)
      const orphanCutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days
      const orphanedRecords = await Record.deleteMany({
        processingStatus: 'failed',
        updatedAt: { $lt: orphanCutoff },
        unifiedEntityId: { $exists: false },
      });

      results.mongoOptimizations.push({
        operation: 'Remove orphaned records',
        deletedCount: orphanedRecords.deletedCount,
      });

      job.progress(30);

      // Remove duplicate potential matches
      const recordsWithDuplicates = await Record.find({
        'potentialMatches.1': { $exists: true }, // Has at least 2 matches
      });

      for (const record of recordsWithDuplicates) {
        const uniqueMatches = [];
        const seenRecordIds = new Set();

        for (const match of record.potentialMatches) {
          if (!seenRecordIds.has(match.recordId.toString())) {
            uniqueMatches.push(match);
            seenRecordIds.add(match.recordId.toString());
          }
        }

        if (uniqueMatches.length !== record.potentialMatches.length) {
          record.potentialMatches = uniqueMatches;
          await record.save();
        }
      }

      results.mongoOptimizations.push({
        operation: 'Remove duplicate potential matches',
        recordsProcessed: recordsWithDuplicates.length,
      });

      job.progress(50);

      // Clean up unified entities without source records
      const entitiesWithoutRecords = await UnifiedEntity.find({});
      let cleanedEntities = 0;

      for (const entity of entitiesWithoutRecords) {
        const recordCount = await Record.countDocuments({ unifiedEntityId: entity._id });
        if (recordCount === 0) {
          await UnifiedEntity.findByIdAndDelete(entity._id);
          cleanedEntities++;
        }
      }

      results.mongoOptimizations.push({
        operation: 'Remove orphaned unified entities',
        deletedCount: cleanedEntities,
      });

      job.progress(70);

    } catch (mongoError) {
      results.errors.push({
        database: 'MongoDB',
        error: mongoError.message,
      });
      
      logger.warn('MongoDB optimization failed', {
        error: mongoError.message,
      });
    }

    // PostgreSQL optimizations (if used)
    try {
      if (pgPool) {
        const client = await pgPool.connect();
        
        try {
          // Analyze tables for better query planning
          await client.query('ANALYZE;');
          
          results.pgOptimizations.push({
            operation: 'Analyze tables',
            status: 'completed',
          });

          // Vacuum to reclaim space
          await client.query('VACUUM;');
          
          results.pgOptimizations.push({
            operation: 'Vacuum tables',
            status: 'completed',
          });

        } finally {
          client.release();
        }
      }
    } catch (pgError) {
      results.errors.push({
        database: 'PostgreSQL',
        error: pgError.message,
      });
      
      logger.warn('PostgreSQL optimization failed', {
        error: pgError.message,
      });
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.jobProcessing('OPTIMIZE_DATABASE_COMPLETED', {
      jobId: job.id,
      mongoOptimizations: results.mongoOptimizations.length,
      pgOptimizations: results.pgOptimizations.length,
      errors: results.errors.length,
      processingTime,
    });

    return {
      success: true,
      ...results,
      processingTime,
    };

  } catch (error) {
    logger.error('Database optimization job failed', {
      jobId: job.id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  cleanupTempFiles,
  archiveOldLogs,
  optimizeDatabase,
};
