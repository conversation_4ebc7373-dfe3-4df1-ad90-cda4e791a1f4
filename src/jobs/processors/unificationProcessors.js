const logger = require('../../utils/logger');
const UnificationService = require('../../services/UnificationService');
const Record = require('../../models/Record');
const UnifiedEntity = require('../../models/UnifiedEntity');

const unificationService = new UnificationService();

// Process entity unification for matched records
const processEntityUnification = async (job) => {
  const { recordId, matches, entityType, manualReview = false } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('ENTITY_UNIFICATION_STARTED', {
      jobId: job.id,
      recordId,
      matchCount: matches.length,
      entityType,
      manualReview,
    });

    // Fetch the primary record
    const primaryRecord = await Record.findById(recordId);
    if (!primaryRecord) {
      throw new Error(`Primary record with ID ${recordId} not found`);
    }

    job.progress(20);

    // Fetch all matched records
    const matchedRecordIds = matches.map(match => match.recordId);
    const matchedRecords = await Record.find({ _id: { $in: matchedRecordIds } });
    
    if (matchedRecords.length !== matchedRecordIds.length) {
      logger.warn('Some matched records not found', {
        expected: matchedRecordIds.length,
        found: matchedRecords.length,
      });
    }

    job.progress(40);

    // Create or update unified entity
    const allRecords = [primaryRecord, ...matchedRecords];
    const unifiedEntity = await unificationService.unifyRecords(allRecords, entityType);
    
    job.progress(70);

    // Update all source records to reference the unified entity
    const updatePromises = allRecords.map(record => {
      record.unifiedEntityId = unifiedEntity._id;
      record.processingStatus = 'unified';
      record.updatedAt = new Date();
      return record.save();
    });

    await Promise.all(updatePromises);

    job.progress(90);

    // Calculate confidence score
    const avgConfidence = matches.reduce((sum, match) => sum + match.confidence, 0) / matches.length;
    
    // Mark for manual review if confidence is below threshold or explicitly requested
    if (manualReview || avgConfidence < 0.95) {
      unifiedEntity.requiresManualReview = true;
      unifiedEntity.reviewReason = manualReview ? 'Manual review requested' : 'Low confidence score';
      await unifiedEntity.save();
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.entityUnification('ENTITY_UNIFICATION_COMPLETED', {
      unifiedEntityId: unifiedEntity._id,
      sourceRecords: allRecords.map(r => r._id),
      canonicalData: unifiedEntity.canonicalData,
      confidence: avgConfidence,
      manualReview: unifiedEntity.requiresManualReview,
    });

    return {
      success: true,
      unifiedEntityId: unifiedEntity._id,
      sourceRecordCount: allRecords.length,
      confidence: avgConfidence,
      requiresManualReview: unifiedEntity.requiresManualReview,
      processingTime,
    };

  } catch (error) {
    logger.error('Entity unification job failed', {
      jobId: job.id,
      recordId,
      error: error.message,
      stack: error.stack,
    });
    
    // Update record status to failed
    try {
      await Record.findByIdAndUpdate(recordId, {
        processingStatus: 'unification_failed',
        processingError: error.message,
        updatedAt: new Date(),
      });
    } catch (updateError) {
      logger.error('Failed to update record status', {
        recordId,
        error: updateError.message,
      });
    }
    
    throw error;
  }
};

// Process batch entity unification
const processBatchUnification = async (job) => {
  const { unificationGroups, entityType, batchSize = 10 } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('BATCH_UNIFICATION_STARTED', {
      jobId: job.id,
      groupCount: unificationGroups.length,
      entityType,
      batchSize,
    });

    const results = {
      processed: 0,
      failed: 0,
      unifiedEntities: [],
      errors: [],
    };

    // Process groups in batches
    for (let i = 0; i < unificationGroups.length; i += batchSize) {
      const batch = unificationGroups.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchPromises = batch.map(async (group) => {
        try {
          const { recordIds, confidence } = group;
          
          // Fetch all records in the group
          const records = await Record.find({ _id: { $in: recordIds } });
          
          if (records.length !== recordIds.length) {
            throw new Error(`Some records in group not found. Expected: ${recordIds.length}, Found: ${records.length}`);
          }

          // Create unified entity
          const unifiedEntity = await unificationService.unifyRecords(records, entityType);
          
          // Update all source records
          const updatePromises = records.map(record => {
            record.unifiedEntityId = unifiedEntity._id;
            record.processingStatus = 'unified';
            record.updatedAt = new Date();
            return record.save();
          });

          await Promise.all(updatePromises);

          // Mark for manual review if confidence is low
          if (confidence < 0.95) {
            unifiedEntity.requiresManualReview = true;
            unifiedEntity.reviewReason = 'Low confidence score';
            await unifiedEntity.save();
          }

          results.processed++;
          results.unifiedEntities.push({
            unifiedEntityId: unifiedEntity._id,
            sourceRecordCount: records.length,
            confidence,
          });
          
          logger.audit.entityUnification('BATCH_ENTITY_UNIFIED', {
            unifiedEntityId: unifiedEntity._id,
            sourceRecords: records.map(r => r._id),
            canonicalData: unifiedEntity.canonicalData,
            confidence,
            manualReview: unifiedEntity.requiresManualReview,
          });

        } catch (error) {
          results.failed++;
          results.errors.push({
            group,
            error: error.message,
          });
          
          logger.error('Batch unification group failed', {
            group,
            error: error.message,
          });

          // Update record statuses to failed
          try {
            await Record.updateMany(
              { _id: { $in: group.recordIds } },
              {
                processingStatus: 'unification_failed',
                processingError: error.message,
                updatedAt: new Date(),
              }
            );
          } catch (updateError) {
            logger.error('Failed to update record statuses', {
              recordIds: group.recordIds,
              error: updateError.message,
            });
          }
        }
      });

      await Promise.all(batchPromises);
      
      // Update progress
      const progress = Math.round(((i + batch.length) / unificationGroups.length) * 100);
      job.progress(progress);
    }

    const processingTime = Date.now() - startTime;
    
    logger.audit.entityUnification('BATCH_UNIFICATION_COMPLETED', {
      unifiedEntityId: null,
      sourceRecords: [],
      canonicalData: null,
      confidence: null,
      manualReview: null,
      processed: results.processed,
      failed: results.failed,
      processingTime,
    });

    return {
      success: true,
      processed: results.processed,
      failed: results.failed,
      unifiedEntities: results.unifiedEntities,
      errors: results.errors,
      processingTime,
    };

  } catch (error) {
    logger.error('Batch unification job failed', {
      jobId: job.id,
      groupCount: unificationGroups.length,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  processEntityUnification,
  processBatchUnification,
};
