const logger = require('../../utils/logger');
const SimilarityService = require('../../services/SimilarityService');
const Record = require('../../models/Record');
const { queueManager, JOB_TYPES, JOB_PRIORITY } = require('../jobQueue');

const similarityService = new SimilarityService();

// Process finding matches for a single record
const processFindMatches = async (job) => {
  const { recordId, entityType, threshold = 0.8 } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('FIND_MATCHES_STARTED', {
      jobId: job.id,
      recordId,
      entityType,
      threshold,
    });

    // Fetch the record
    const record = await Record.findById(recordId);
    if (!record) {
      throw new Error(`Record with ID ${recordId} not found`);
    }

    if (!record.normalizedData) {
      throw new Error(`Record ${recordId} has not been normalized yet`);
    }

    job.progress(20);

    // Find potential matches
    const matches = await similarityService.findMatches(record, entityType, threshold);
    
    job.progress(70);

    // Update record with match results
    record.potentialMatches = matches.map(match => ({
      recordId: match.recordId,
      score: match.score,
      matchedFields: match.matchedFields,
      confidence: match.confidence,
    }));
    record.processingStatus = 'matched';
    record.updatedAt = new Date();
    await record.save();

    job.progress(90);

    // Queue entity unification if high-confidence matches found
    const highConfidenceMatches = matches.filter(match => match.confidence >= 0.9);
    if (highConfidenceMatches.length > 0 && record.autoProcess !== false) {
      await queueManager.addJob('entityUnification', JOB_TYPES.UNIFY_ENTITIES, {
        recordId: record._id,
        matches: highConfidenceMatches,
        entityType,
      }, {
        priority: JOB_PRIORITY.NORMAL,
      });
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.similarityMatching('FIND_MATCHES_COMPLETED', {
      recordId: record._id,
      candidateMatches: matches.length,
      matchScores: matches.map(m => m.score),
      threshold,
      processingTime,
    });

    return {
      success: true,
      recordId: record._id,
      matches: matches.length,
      highConfidenceMatches: highConfidenceMatches.length,
      processingTime,
    };

  } catch (error) {
    logger.error('Find matches job failed', {
      jobId: job.id,
      recordId,
      error: error.message,
      stack: error.stack,
    });
    
    // Update record status to failed
    try {
      await Record.findByIdAndUpdate(recordId, {
        processingStatus: 'match_failed',
        processingError: error.message,
        updatedAt: new Date(),
      });
    } catch (updateError) {
      logger.error('Failed to update record status', {
        recordId,
        error: updateError.message,
      });
    }
    
    throw error;
  }
};

// Process batch matching
const processBatchMatching = async (job) => {
  const { recordIds, entityType, threshold = 0.8, batchSize = 20 } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('BATCH_MATCHING_STARTED', {
      jobId: job.id,
      recordCount: recordIds.length,
      entityType,
      threshold,
      batchSize,
    });

    const results = {
      processed: 0,
      failed: 0,
      totalMatches: 0,
      highConfidenceMatches: 0,
      errors: [],
    };

    // Process records in batches
    for (let i = 0; i < recordIds.length; i += batchSize) {
      const batch = recordIds.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchPromises = batch.map(async (recordId) => {
        try {
          const record = await Record.findById(recordId);
          if (!record) {
            throw new Error(`Record with ID ${recordId} not found`);
          }

          if (!record.normalizedData) {
            throw new Error(`Record ${recordId} has not been normalized yet`);
          }

          const matches = await similarityService.findMatches(record, entityType, threshold);
          
          record.potentialMatches = matches.map(match => ({
            recordId: match.recordId,
            score: match.score,
            matchedFields: match.matchedFields,
            confidence: match.confidence,
          }));
          record.processingStatus = 'matched';
          record.updatedAt = new Date();
          await record.save();

          // Queue entity unification for high-confidence matches
          const highConfidenceMatches = matches.filter(match => match.confidence >= 0.9);
          if (highConfidenceMatches.length > 0 && record.autoProcess !== false) {
            await queueManager.addJob('entityUnification', JOB_TYPES.UNIFY_ENTITIES, {
              recordId: record._id,
              matches: highConfidenceMatches,
              entityType,
            }, {
              priority: JOB_PRIORITY.LOW, // Lower priority for batch processing
            });
          }

          results.processed++;
          results.totalMatches += matches.length;
          results.highConfidenceMatches += highConfidenceMatches.length;
          
          logger.audit.similarityMatching('BATCH_RECORD_MATCHED', {
            recordId: record._id,
            candidateMatches: matches.length,
            matchScores: matches.map(m => m.score),
            threshold,
            processingTime: Date.now() - startTime,
          });

        } catch (error) {
          results.failed++;
          results.errors.push({
            recordId,
            error: error.message,
          });
          
          logger.error('Batch matching record failed', {
            recordId,
            error: error.message,
          });

          // Update record status to failed
          try {
            await Record.findByIdAndUpdate(recordId, {
              processingStatus: 'match_failed',
              processingError: error.message,
              updatedAt: new Date(),
            });
          } catch (updateError) {
            logger.error('Failed to update record status', {
              recordId,
              error: updateError.message,
            });
          }
        }
      });

      await Promise.all(batchPromises);
      
      // Update progress
      const progress = Math.round(((i + batch.length) / recordIds.length) * 100);
      job.progress(progress);
    }

    const processingTime = Date.now() - startTime;
    
    logger.audit.similarityMatching('BATCH_MATCHING_COMPLETED', {
      recordId: null,
      candidateMatches: results.totalMatches,
      matchScores: [],
      threshold,
      processingTime,
      processed: results.processed,
      failed: results.failed,
    });

    return {
      success: true,
      processed: results.processed,
      failed: results.failed,
      totalMatches: results.totalMatches,
      highConfidenceMatches: results.highConfidenceMatches,
      errors: results.errors,
      processingTime,
    };

  } catch (error) {
    logger.error('Batch matching job failed', {
      jobId: job.id,
      recordCount: recordIds.length,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  processFindMatches,
  processBatchMatching,
};
