const logger = require('../../utils/logger');
const PreprocessingService = require('../../services/PreprocessingService');
const Record = require('../../models/Record');
const { queueManager, JOB_TYPES, JOB_PRIORITY } = require('../jobQueue');

const preprocessingService = new PreprocessingService();

// Process single record normalization
const processRecordNormalization = async (job) => {
  const { recordId, entityType } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('RECORD_NORMALIZATION_STARTED', {
      jobId: job.id,
      recordId,
      entityType,
    });

    // Fetch the record
    const record = await Record.findById(recordId);
    if (!record) {
      throw new Error(`Record with ID ${recordId} not found`);
    }

    job.progress(20);

    // Perform preprocessing
    const normalizedData = await preprocessingService.normalizeRecord(record.originalData, entityType);
    
    job.progress(60);

    // Update record with normalized data
    record.normalizedData = normalizedData;
    record.processingStatus = 'normalized';
    record.updatedAt = new Date();
    await record.save();

    job.progress(80);

    // Queue similarity matching if auto-processing is enabled
    if (record.autoProcess !== false) {
      await queueManager.addJob('similarityMatching', JOB_TYPES.FIND_MATCHES, {
        recordId: record._id,
        entityType,
      }, {
        priority: JOB_PRIORITY.NORMAL,
      });
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.preprocessing('RECORD_NORMALIZATION_COMPLETED', {
      recordId: record._id,
      originalData: record.originalData,
      normalizedData: normalizedData,
      processingTime,
    });

    return {
      success: true,
      recordId: record._id,
      normalizedData,
      processingTime,
    };

  } catch (error) {
    logger.error('Record normalization job failed', {
      jobId: job.id,
      recordId,
      error: error.message,
      stack: error.stack,
    });
    
    // Update record status to failed
    try {
      await Record.findByIdAndUpdate(recordId, {
        processingStatus: 'failed',
        processingError: error.message,
        updatedAt: new Date(),
      });
    } catch (updateError) {
      logger.error('Failed to update record status', {
        recordId,
        error: updateError.message,
      });
    }
    
    throw error;
  }
};

// Process batch normalization
const processBatchNormalization = async (job) => {
  const { recordIds, entityType, batchSize = 50 } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('BATCH_NORMALIZATION_STARTED', {
      jobId: job.id,
      recordCount: recordIds.length,
      entityType,
      batchSize,
    });

    const results = {
      processed: 0,
      failed: 0,
      errors: [],
    };

    // Process records in batches
    for (let i = 0; i < recordIds.length; i += batchSize) {
      const batch = recordIds.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchPromises = batch.map(async (recordId) => {
        try {
          const record = await Record.findById(recordId);
          if (!record) {
            throw new Error(`Record with ID ${recordId} not found`);
          }

          const normalizedData = await preprocessingService.normalizeRecord(record.originalData, entityType);
          
          record.normalizedData = normalizedData;
          record.processingStatus = 'normalized';
          record.updatedAt = new Date();
          await record.save();

          // Queue similarity matching
          if (record.autoProcess !== false) {
            await queueManager.addJob('similarityMatching', JOB_TYPES.FIND_MATCHES, {
              recordId: record._id,
              entityType,
            }, {
              priority: JOB_PRIORITY.LOW, // Lower priority for batch processing
            });
          }

          results.processed++;
          
          logger.audit.preprocessing('BATCH_RECORD_NORMALIZED', {
            recordId: record._id,
            originalData: record.originalData,
            normalizedData: normalizedData,
            processingTime: Date.now() - startTime,
          });

        } catch (error) {
          results.failed++;
          results.errors.push({
            recordId,
            error: error.message,
          });
          
          logger.error('Batch normalization record failed', {
            recordId,
            error: error.message,
          });

          // Update record status to failed
          try {
            await Record.findByIdAndUpdate(recordId, {
              processingStatus: 'failed',
              processingError: error.message,
              updatedAt: new Date(),
            });
          } catch (updateError) {
            logger.error('Failed to update record status', {
              recordId,
              error: updateError.message,
            });
          }
        }
      });

      await Promise.all(batchPromises);
      
      // Update progress
      const progress = Math.round(((i + batch.length) / recordIds.length) * 100);
      job.progress(progress);
    }

    const processingTime = Date.now() - startTime;
    
    logger.audit.preprocessing('BATCH_NORMALIZATION_COMPLETED', {
      recordId: null,
      originalData: null,
      normalizedData: null,
      processingTime,
      processed: results.processed,
      failed: results.failed,
    });

    return {
      success: true,
      processed: results.processed,
      failed: results.failed,
      errors: results.errors,
      processingTime,
    };

  } catch (error) {
    logger.error('Batch normalization job failed', {
      jobId: job.id,
      recordCount: recordIds.length,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  processRecordNormalization,
  processBatchNormalization,
};
