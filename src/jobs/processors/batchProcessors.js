const logger = require('../../utils/logger');
const Record = require('../../models/Record');
const { queueManager, JOB_TYPES, JOB_PRIORITY } = require('../jobQueue');

// Process entire dataset through the pipeline
const processDataset = async (job) => {
  const { 
    source, 
    entityType, 
    batchSize = 100,
    skipNormalized = false,
    skipMatched = false,
    skipUnified = false 
  } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('DATASET_PROCESSING_STARTED', {
      jobId: job.id,
      source,
      entityType,
      batchSize,
      skipNormalized,
      skipMatched,
      skipUnified,
    });

    // Build query for records to process
    const query = { source, entityType };
    
    if (skipNormalized) {
      query.processingStatus = { $ne: 'normalized' };
    }
    if (skipMatched) {
      query.processingStatus = { $nin: ['matched', 'unified'] };
    }
    if (skipUnified) {
      query.processingStatus = { $ne: 'unified' };
    }

    // Get total count for progress tracking
    const totalRecords = await Record.countDocuments(query);
    
    if (totalRecords === 0) {
      logger.info('No records found to process', { query });
      return {
        success: true,
        message: 'No records found to process',
        totalRecords: 0,
      };
    }

    job.progress(5);

    const results = {
      totalRecords,
      processed: 0,
      normalizationJobs: 0,
      matchingJobs: 0,
      unificationJobs: 0,
    };

    // Process records in batches
    let skip = 0;
    while (skip < totalRecords) {
      const records = await Record.find(query)
        .skip(skip)
        .limit(batchSize)
        .select('_id processingStatus');

      if (records.length === 0) {
        break;
      }

      // Group records by processing status
      const recordsByStatus = {
        pending: [],
        normalized: [],
        matched: [],
      };

      records.forEach(record => {
        switch (record.processingStatus) {
          case 'pending':
          case 'failed':
            recordsByStatus.pending.push(record._id);
            break;
          case 'normalized':
          case 'match_failed':
            recordsByStatus.normalized.push(record._id);
            break;
          case 'matched':
          case 'unification_failed':
            recordsByStatus.matched.push(record._id);
            break;
        }
      });

      // Queue normalization jobs for pending records
      if (recordsByStatus.pending.length > 0 && !skipNormalized) {
        await queueManager.addJob('preprocessing', JOB_TYPES.BATCH_NORMALIZE, {
          recordIds: recordsByStatus.pending,
          entityType,
          batchSize: Math.min(batchSize, 50),
        }, {
          priority: JOB_PRIORITY.LOW,
        });
        results.normalizationJobs++;
      }

      // Queue matching jobs for normalized records
      if (recordsByStatus.normalized.length > 0 && !skipMatched) {
        await queueManager.addJob('similarityMatching', JOB_TYPES.BATCH_MATCH, {
          recordIds: recordsByStatus.normalized,
          entityType,
          batchSize: Math.min(batchSize, 20),
        }, {
          priority: JOB_PRIORITY.LOW,
        });
        results.matchingJobs++;
      }

      // Queue unification jobs for matched records
      if (recordsByStatus.matched.length > 0 && !skipUnified) {
        // Group matched records by their potential matches for batch unification
        const matchedRecords = await Record.find({ 
          _id: { $in: recordsByStatus.matched },
          'potentialMatches.0': { $exists: true }
        }).select('_id potentialMatches');

        const unificationGroups = [];
        const processedRecords = new Set();

        matchedRecords.forEach(record => {
          if (processedRecords.has(record._id.toString())) {
            return;
          }

          const highConfidenceMatches = record.potentialMatches
            .filter(match => match.confidence >= 0.9)
            .map(match => match.recordId);

          if (highConfidenceMatches.length > 0) {
            const groupRecords = [record._id, ...highConfidenceMatches];
            const avgConfidence = record.potentialMatches
              .filter(match => match.confidence >= 0.9)
              .reduce((sum, match) => sum + match.confidence, 0) / 
              record.potentialMatches.filter(match => match.confidence >= 0.9).length;

            unificationGroups.push({
              recordIds: groupRecords,
              confidence: avgConfidence,
            });

            // Mark all records in this group as processed
            groupRecords.forEach(id => processedRecords.add(id.toString()));
          }
        });

        if (unificationGroups.length > 0) {
          await queueManager.addJob('entityUnification', JOB_TYPES.BATCH_UNIFY, {
            unificationGroups,
            entityType,
            batchSize: Math.min(batchSize, 10),
          }, {
            priority: JOB_PRIORITY.LOW,
          });
          results.unificationJobs++;
        }
      }

      results.processed += records.length;
      skip += batchSize;

      // Update progress
      const progress = Math.round((skip / totalRecords) * 95) + 5; // Reserve 5% for completion
      job.progress(progress);
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.jobProcessing('DATASET_PROCESSING_COMPLETED', {
      jobId: job.id,
      source,
      entityType,
      totalRecords: results.totalRecords,
      processed: results.processed,
      normalizationJobs: results.normalizationJobs,
      matchingJobs: results.matchingJobs,
      unificationJobs: results.unificationJobs,
      processingTime,
    });

    return {
      success: true,
      ...results,
      processingTime,
    };

  } catch (error) {
    logger.error('Dataset processing job failed', {
      jobId: job.id,
      source,
      entityType,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Reprocess failed jobs
const reprocessFailedJobs = async (job) => {
  const { 
    source, 
    entityType, 
    failureTypes = ['failed', 'match_failed', 'unification_failed'],
    maxAge = 24 * 60 * 60 * 1000 // 24 hours
  } = job.data;
  
  try {
    job.progress(0);
    const startTime = Date.now();
    
    logger.audit.jobProcessing('REPROCESS_FAILED_STARTED', {
      jobId: job.id,
      source,
      entityType,
      failureTypes,
      maxAge,
    });

    // Find failed records within the specified time window
    const cutoffDate = new Date(Date.now() - maxAge);
    const query = {
      source,
      entityType,
      processingStatus: { $in: failureTypes },
      updatedAt: { $gte: cutoffDate },
    };

    const failedRecords = await Record.find(query).select('_id processingStatus');
    
    if (failedRecords.length === 0) {
      logger.info('No failed records found to reprocess', { query });
      return {
        success: true,
        message: 'No failed records found to reprocess',
        totalRecords: 0,
      };
    }

    job.progress(20);

    const results = {
      totalRecords: failedRecords.length,
      requeued: 0,
      errors: [],
    };

    // Group records by failure type and requeue appropriately
    const recordsByFailureType = {
      failed: [],
      match_failed: [],
      unification_failed: [],
    };

    failedRecords.forEach(record => {
      if (recordsByFailureType[record.processingStatus]) {
        recordsByFailureType[record.processingStatus].push(record._id);
      }
    });

    // Reset status and requeue normalization for general failures
    if (recordsByFailureType.failed.length > 0) {
      await Record.updateMany(
        { _id: { $in: recordsByFailureType.failed } },
        { 
          processingStatus: 'pending',
          processingError: null,
          updatedAt: new Date(),
        }
      );

      await queueManager.addJob('preprocessing', JOB_TYPES.BATCH_NORMALIZE, {
        recordIds: recordsByFailureType.failed,
        entityType,
      }, {
        priority: JOB_PRIORITY.LOW,
      });

      results.requeued += recordsByFailureType.failed.length;
    }

    job.progress(60);

    // Requeue matching for match failures
    if (recordsByFailureType.match_failed.length > 0) {
      await Record.updateMany(
        { _id: { $in: recordsByFailureType.match_failed } },
        { 
          processingStatus: 'normalized',
          processingError: null,
          updatedAt: new Date(),
        }
      );

      await queueManager.addJob('similarityMatching', JOB_TYPES.BATCH_MATCH, {
        recordIds: recordsByFailureType.match_failed,
        entityType,
      }, {
        priority: JOB_PRIORITY.LOW,
      });

      results.requeued += recordsByFailureType.match_failed.length;
    }

    job.progress(80);

    // Requeue unification for unification failures
    if (recordsByFailureType.unification_failed.length > 0) {
      await Record.updateMany(
        { _id: { $in: recordsByFailureType.unification_failed } },
        { 
          processingStatus: 'matched',
          processingError: null,
          updatedAt: new Date(),
        }
      );

      // Create individual unification jobs for failed records
      for (const recordId of recordsByFailureType.unification_failed) {
        const record = await Record.findById(recordId).select('potentialMatches');
        if (record && record.potentialMatches && record.potentialMatches.length > 0) {
          const highConfidenceMatches = record.potentialMatches
            .filter(match => match.confidence >= 0.8); // Lower threshold for retry

          if (highConfidenceMatches.length > 0) {
            await queueManager.addJob('entityUnification', JOB_TYPES.UNIFY_ENTITIES, {
              recordId,
              matches: highConfidenceMatches,
              entityType,
              manualReview: true, // Flag for manual review since it failed before
            }, {
              priority: JOB_PRIORITY.LOW,
            });
          }
        }
      }

      results.requeued += recordsByFailureType.unification_failed.length;
    }

    job.progress(100);
    
    const processingTime = Date.now() - startTime;
    
    logger.audit.jobProcessing('REPROCESS_FAILED_COMPLETED', {
      jobId: job.id,
      source,
      entityType,
      totalRecords: results.totalRecords,
      requeued: results.requeued,
      processingTime,
    });

    return {
      success: true,
      ...results,
      processingTime,
    };

  } catch (error) {
    logger.error('Reprocess failed jobs failed', {
      jobId: job.id,
      source,
      entityType,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  processDataset,
  reprocessFailedJobs,
};
