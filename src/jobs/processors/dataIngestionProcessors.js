const fs = require('fs');
const csv = require('csv-parser');
const logger = require('../../utils/logger');
const DataIngestionService = require('../../services/DataIngestionService');
const { queueManager, JOB_TYPES, JOB_PRIORITY } = require('../jobQueue');

const dataIngestionService = new DataIngestionService();

// Process CSV file ingestion
const processCSVIngestion = async (job) => {
  const { filePath, options } = job.data;
  
  try {
    job.progress(0);
    logger.audit.jobProcessing('CSV_INGESTION_STARTED', {
      jobId: job.id,
      filePath,
      options,
    });

    const results = await dataIngestionService.ingestFromCSV(filePath, options);
    
    job.progress(50);

    // Queue preprocessing for each ingested record
    if (options.autoProcess !== false) {
      for (const recordId of results.recordIds) {
        await queueManager.addJob('preprocessing', JOB_TYPES.NORMALIZE_RECORD, {
          recordId,
          entityType: options.entityType,
        }, {
          priority: JOB_PRIORITY.NORMAL,
        });
      }
    }

    job.progress(100);
    
    logger.audit.dataIngestion('CSV_INGESTION_COMPLETED', {
      recordId: null,
      source: options.source,
      entityType: options.entityType,
      recordCount: results.recordCount,
    });

    return {
      success: true,
      recordCount: results.recordCount,
      recordIds: results.recordIds,
      skipped: results.skipped,
    };

  } catch (error) {
    logger.error('CSV ingestion job failed', {
      jobId: job.id,
      filePath,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Process JSON data ingestion
const processJSONIngestion = async (job) => {
  const { data, options } = job.data;
  
  try {
    job.progress(0);
    logger.audit.jobProcessing('JSON_INGESTION_STARTED', {
      jobId: job.id,
      recordCount: Array.isArray(data) ? data.length : 1,
      options,
    });

    const results = await dataIngestionService.ingestFromJSON(data, options);
    
    job.progress(50);

    // Queue preprocessing for each ingested record
    if (options.autoProcess !== false) {
      for (const recordId of results.recordIds) {
        await queueManager.addJob('preprocessing', JOB_TYPES.NORMALIZE_RECORD, {
          recordId,
          entityType: options.entityType,
        }, {
          priority: JOB_PRIORITY.NORMAL,
        });
      }
    }

    job.progress(100);
    
    logger.audit.dataIngestion('JSON_INGESTION_COMPLETED', {
      recordId: null,
      source: options.source,
      entityType: options.entityType,
      recordCount: results.recordCount,
    });

    return {
      success: true,
      recordCount: results.recordCount,
      recordIds: results.recordIds,
      skipped: results.skipped,
    };

  } catch (error) {
    logger.error('JSON ingestion job failed', {
      jobId: job.id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// Process single record ingestion
const processSingleRecord = async (job) => {
  const { recordData, options } = job.data;
  
  try {
    job.progress(0);
    logger.audit.jobProcessing('SINGLE_RECORD_INGESTION_STARTED', {
      jobId: job.id,
      recordData: JSON.stringify(recordData).substring(0, 200),
      options,
    });

    const record = await dataIngestionService.ingestRecord(recordData, options);
    
    job.progress(50);

    // Queue preprocessing if auto-processing is enabled
    if (options.autoProcess !== false) {
      await queueManager.addJob('preprocessing', JOB_TYPES.NORMALIZE_RECORD, {
        recordId: record._id,
        entityType: options.entityType,
      }, {
        priority: JOB_PRIORITY.NORMAL,
      });
    }

    job.progress(100);
    
    logger.audit.dataIngestion('SINGLE_RECORD_INGESTION_COMPLETED', {
      recordId: record._id,
      source: options.source,
      entityType: options.entityType,
      recordCount: 1,
    });

    return {
      success: true,
      recordId: record._id,
      record: record,
    };

  } catch (error) {
    logger.error('Single record ingestion job failed', {
      jobId: job.id,
      recordData: JSON.stringify(recordData).substring(0, 200),
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  processCSVIngestion,
  processJSONIngestion,
  processSingleRecord,
};
