const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for structured logging
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };

    // Add process information
    logEntry.pid = process.pid;
    logEntry.hostname = require('os').hostname();

    return JSON.stringify(logEntry);
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: customFormat,
  defaultMeta: {
    service: 'data-unification-system',
    version: process.env.npm_package_version || '1.0.0'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),

    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 10,
      tailable: true
    }),

    // Audit log file for unification operations
    new winston.transports.File({
      filename: path.join(logsDir, 'audit.log'),
      level: 'info',
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 20,
      tailable: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],

  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 10 * 1024 * 1024,
      maxFiles: 5
    })
  ],

  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 10 * 1024 * 1024,
      maxFiles: 5
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Specialized logging methods for the data unification system
const auditLogger = {
  // Log data ingestion operations
  dataIngestion: (operation, data) => {
    logger.info('Data ingestion operation', {
      operation,
      recordId: data.recordId,
      source: data.source,
      entityType: data.entityType,
      recordCount: data.recordCount,
      timestamp: new Date().toISOString(),
      category: 'DATA_INGESTION'
    });
  },

  // Log preprocessing operations
  preprocessing: (operation, data) => {
    logger.info('Preprocessing operation', {
      operation,
      recordId: data.recordId,
      originalData: data.originalData,
      normalizedData: data.normalizedData,
      processingTime: data.processingTime,
      timestamp: new Date().toISOString(),
      category: 'PREPROCESSING'
    });
  },

  // Log similarity matching operations
  similarityMatching: (operation, data) => {
    logger.info('Similarity matching operation', {
      operation,
      recordId: data.recordId,
      candidateMatches: data.candidateMatches,
      matchScores: data.matchScores,
      threshold: data.threshold,
      processingTime: data.processingTime,
      timestamp: new Date().toISOString(),
      category: 'SIMILARITY_MATCHING'
    });
  },

  // Log entity unification operations
  entityUnification: (operation, data) => {
    logger.info('Entity unification operation', {
      operation,
      unifiedEntityId: data.unifiedEntityId,
      sourceRecords: data.sourceRecords,
      canonicalData: data.canonicalData,
      confidence: data.confidence,
      manualReview: data.manualReview,
      timestamp: new Date().toISOString(),
      category: 'ENTITY_UNIFICATION'
    });
  },

  // Log manual review operations
  manualReview: (operation, data) => {
    logger.info('Manual review operation', {
      operation,
      reviewerId: data.reviewerId,
      entityId: data.entityId,
      decision: data.decision,
      reason: data.reason,
      timestamp: new Date().toISOString(),
      category: 'MANUAL_REVIEW'
    });
  },

  // Log job processing operations
  jobProcessing: (operation, data) => {
    logger.info('Job processing operation', {
      operation,
      jobId: data.jobId,
      jobType: data.jobType,
      status: data.status,
      progress: data.progress,
      error: data.error,
      processingTime: data.processingTime,
      timestamp: new Date().toISOString(),
      category: 'JOB_PROCESSING'
    });
  },

  // Log system performance metrics
  performance: (operation, data) => {
    logger.info('Performance metrics', {
      operation,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      customMetrics: data,
      timestamp: new Date().toISOString(),
      category: 'PERFORMANCE'
    });
  }
};

// Enhanced logger with audit capabilities
const enhancedLogger = {
  ...logger,
  audit: auditLogger,

  // Convenience method for request logging
  logRequest: (req, res, next) => {
    const start = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - start;
      logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        category: 'HTTP_REQUEST'
      });
    });

    if (next) next();
  },

  // Method for logging with correlation ID
  withCorrelationId: (correlationId) => {
    return {
      debug: (message, meta = {}) => logger.debug(message, { ...meta, correlationId }),
      info: (message, meta = {}) => logger.info(message, { ...meta, correlationId }),
      warn: (message, meta = {}) => logger.warn(message, { ...meta, correlationId }),
      error: (message, meta = {}) => logger.error(message, { ...meta, correlationId })
    };
  }
};

module.exports = enhancedLogger;