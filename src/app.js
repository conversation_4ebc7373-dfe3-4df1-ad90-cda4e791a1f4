const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const { connectDB } = require('./config/database.js');
const { initializeRedis } = require('./config/redis.js');
const { setupJobs } = require('./jobs/jobSetup.js');
const logger = require('./utils/logger.js');

// Import routes
const dataRoutes = require('./routes/dataRoutes.js');
const unificationRoutes = require('./routes/unificationRoutes.js');
const adminRoutes = require('./routes/adminRoutes.js');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/data', dataRoutes);
app.use('/api/unification', unificationRoutes);
app.use('/api/admin', adminRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Initialize application
async function startServer() {
  try {
    await connectDB();
    await initializeRedis();
    await setupJobs();
    
    app.listen(PORT, () => {
      logger.info(`Data Unification System running on port ${PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();