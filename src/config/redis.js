const Redis = require('ioredis');
const logger = require('../utils/logger.js');

let redis;

const initializeRedis = async () => {
  try {
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      enableOfflineQueue: false,
    });

    redis.on('connect', () => {
      logger.info('Redis connected successfully');
    });

    redis.on('error', (err) => {
      logger.error('Redis connection error:', err);
    });

    return redis;
  } catch (error) {
    logger.error('Redis initialization failed:', error);
    throw error;
  }
};

const getRedis = () => redis;

module.exports = { initializeRedis, getRedis };