# Test Suite Documentation

This directory contains comprehensive tests for the Intelligent Data Unification System, covering all major components including logging, job processing, and data flow integration.

## Test Structure

```
tests/
├── integration/           # Integration tests
│   ├── logger.test.js    # Logger system tests
│   ├── jobQueue.test.js  # Job queue system tests
│   ├── dataFlow.test.js  # End-to-end data flow tests
│   └── jobProcessors.test.js # Job processor tests
├── unit/                 # Unit tests (to be added)
├── setup.js             # Global test setup
├── globalSetup.js       # Jest global setup
├── globalTeardown.js    # Jest global teardown
└── README.md           # This file
```

## Integration Tests

### Logger Tests (`logger.test.js`)
Tests the comprehensive logging system including:
- Log file creation and management
- Different log levels (debug, info, warn, error)
- Structured logging with metadata
- Audit logging for all system operations:
  - Data ingestion operations
  - Preprocessing operations
  - Similarity matching operations
  - Entity unification operations
  - Job processing operations
  - Performance metrics
- Correlation ID support
- Error handling and stack traces

### Job Queue Tests (`jobQueue.test.js`)
Tests the Bull-based job queue system including:
- Job creation and queuing for all queue types:
  - Data ingestion queue
  - Preprocessing queue
  - Similarity matching queue
  - Entity unification queue
  - Batch processing queue
  - Cleanup queue
- Job status tracking and monitoring
- Queue statistics and metrics
- Queue management (pause, resume, clean)
- Job priorities and scheduling
- Error handling and retry mechanisms

### Data Flow Tests (`dataFlow.test.js`)
Tests the complete data unification workflow including:
- End-to-end data processing pipeline
- Data ingestion from JSON and API sources
- Text normalization for Arabic and English
- Language detection and transliteration
- Similarity matching and scoring
- Entity unification and clustering
- Duplicate detection and handling
- Audit trail creation and management
- Processing status updates

### Job Processor Tests (`jobProcessors.test.js`)
Tests all job processors including:
- Data ingestion processors (CSV, JSON, single record)
- Preprocessing processors (normalization, batch processing)
- Similarity processors (matching, batch matching)
- Unification processors (entity creation, batch unification)
- Batch processors (dataset processing, failed job reprocessing)
- Cleanup processors (file cleanup, log archiving, database optimization)

## Running Tests

### All Tests
```bash
npm test
```

### Integration Tests Only
```bash
npm run test:integration
```

### With Coverage Report
```bash
npm run test:coverage
```

### Watch Mode (for development)
```bash
npm run test:watch
```

### Verbose Output
```bash
npm run test:verbose
```

## Test Configuration

The test suite uses Jest with the following configuration:
- **Environment**: Node.js
- **Timeout**: 30 seconds for integration tests
- **Coverage**: Enabled with HTML and LCOV reports
- **Mocking**: External dependencies (Redis, MongoDB, Bull) are mocked
- **Setup**: Global setup and teardown for test environment

## Test Data and Mocking

### Mocked Dependencies
- **Redis**: Mocked with ioredis mock
- **MongoDB**: Uses in-memory MongoDB for integration tests
- **Bull Queues**: Mocked with job simulation
- **File System**: Mocked for cleanup processor tests

### Test Utilities
Global test utilities are available in all tests:
- `testUtils.createTestRecord()`: Creates test record objects
- `testUtils.createTestUnifiedEntity()`: Creates test unified entity objects
- `testUtils.createMockJob()`: Creates mock job objects
- `testUtils.wait()`: Async wait helper
- `testUtils.generateRandomString()`: Random string generator

## Coverage Goals

The test suite aims for:
- **Line Coverage**: > 80%
- **Function Coverage**: > 85%
- **Branch Coverage**: > 75%
- **Statement Coverage**: > 80%

## Test Environment Variables

Tests use the following environment variables:
- `NODE_ENV=test`
- `LOG_LEVEL=error` (to reduce test output noise)
- `MONGODB_URI=mongodb://localhost:27017/data_unification_test`
- `REDIS_HOST=localhost`
- `REDIS_PORT=6379`

## Adding New Tests

When adding new tests:

1. **Unit Tests**: Add to `tests/unit/` directory
2. **Integration Tests**: Add to `tests/integration/` directory
3. **Follow Naming Convention**: `*.test.js` or `*.spec.js`
4. **Use Test Utilities**: Leverage global test utilities for consistency
5. **Mock External Dependencies**: Use provided mocks or create new ones
6. **Add Documentation**: Update this README when adding new test categories

## Continuous Integration

The test suite is designed to run in CI/CD environments:
- All external dependencies are mocked
- Tests are isolated and can run in parallel
- Coverage reports are generated in CI-friendly formats
- Test results are verbose for debugging

## Troubleshooting

### Common Issues

1. **MongoDB Connection Errors**: Ensure MongoDB is running or use in-memory server
2. **Redis Connection Errors**: Redis is mocked, check mock configuration
3. **File Permission Errors**: Ensure test directories are writable
4. **Timeout Errors**: Increase Jest timeout for slow operations

### Debug Mode

To run tests with debug output:
```bash
DEBUG=* npm test
```

### Cleaning Test Environment

Test cleanup is automatic, but manual cleanup:
```bash
rm -rf logs/test* uploads/test* temp/test*
```

## Performance Considerations

- Tests use mocked dependencies for speed
- In-memory database for integration tests
- Parallel test execution where possible
- Cleanup between test runs to prevent interference
