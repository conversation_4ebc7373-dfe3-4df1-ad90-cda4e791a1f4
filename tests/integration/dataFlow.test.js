const mongoose = require('mongoose');
// Mock MongoMemoryServer for testing
const MongoMemoryServer = {
  create: jest.fn().mockResolvedValue({
    getUri: jest.fn().mockReturnValue('mongodb://localhost:27017/test'),
    stop: jest.fn().mockResolvedValue(true)
  })
};
const DataIngestionService = require('../../src/services/DataIngestionService');
const PreprocessingService = require('../../src/services/PreprocessingService');
const SimilarityService = require('../../src/services/SimilarityService');
const UnificationService = require('../../src/services/UnificationService');
const Record = require('../../src/models/Record');
const UnifiedEntity = require('../../src/models/UnifiedEntity');
const logger = require('../../src/utils/logger');

describe('Data Flow Integration Tests', () => {
  let mongoServer;
  let dataIngestionService;
  let preprocessingService;
  let similarityService;
  let unificationService;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Initialize services
    dataIngestionService = new DataIngestionService();
    preprocessingService = new PreprocessingService();
    similarityService = new SimilarityService();
    unificationService = new UnificationService();
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await Record.deleteMany({});
    await UnifiedEntity.deleteMany({});
  });

  test('should complete full data unification workflow', async () => {
    // Step 1: Data Ingestion
    const testData = [
      {
        name: 'Ahmed Mohamed Pharmacy',
        address: 'Cairo, Egypt',
        phone: '**********0',
        license: 'PH001'
      },
      {
        name: 'احمد محمد صيدلية',
        address: 'القاهرة، مصر',
        phone: '**********0',
        license: 'PH001'
      },
      {
        name: 'Ahmed M. Pharmacy',
        address: 'Cairo, Egypt',
        phone: '**********0'
      }
    ];

    const ingestionResults = await dataIngestionService.ingestFromJSON(testData, {
      source: 'test-integration',
      entityType: 'pharmacy',
      skipDuplicates: false
    });

    expect(ingestionResults.recordCount).toBe(3);
    expect(ingestionResults.recordIds).toHaveLength(3);

    // Verify records were created
    const records = await Record.find({ source: 'test-integration' });
    expect(records).toHaveLength(3);

    // Step 2: Preprocessing
    for (const recordId of ingestionResults.recordIds) {
      const record = await Record.findById(recordId);
      const normalizedData = await preprocessingService.normalizeRecord(record.originalData, 'pharmacy');
      
      record.normalizedData = normalizedData;
      record.processingStatus = 'normalized';
      await record.save();
    }

    // Verify normalization
    const normalizedRecords = await Record.find({ 
      source: 'test-integration',
      processingStatus: 'normalized'
    });
    expect(normalizedRecords).toHaveLength(3);
    
    for (const record of normalizedRecords) {
      expect(record.normalizedData).toBeDefined();
      expect(record.normalizedData.name).toBeDefined();
      expect(record.normalizedData.nameTokens).toBeDefined();
    }

    // Step 3: Similarity Matching
    const targetRecord = normalizedRecords[0];
    const matches = await similarityService.findMatches(targetRecord, 'pharmacy', 0.7);
    
    expect(matches).toBeDefined();
    expect(Array.isArray(matches)).toBe(true);
    
    // Update record with matches
    targetRecord.potentialMatches = matches.map(match => ({
      recordId: match.recordId,
      score: match.score,
      confidence: match.confidence,
      matchedFields: match.matchedFields
    }));
    targetRecord.processingStatus = 'matched';
    await targetRecord.save();

    // Step 4: Entity Unification
    if (matches.length > 0) {
      const recordsToUnify = [targetRecord];
      
      // Add matched records
      for (const match of matches.slice(0, 2)) { // Limit to first 2 matches
        const matchedRecord = await Record.findById(match.recordId);
        if (matchedRecord) {
          recordsToUnify.push(matchedRecord);
        }
      }

      const unifiedEntity = await unificationService.unifyRecords(recordsToUnify, 'pharmacy');
      
      expect(unifiedEntity).toBeDefined();
      expect(unifiedEntity.entityType).toBe('pharmacy');
      expect(unifiedEntity.canonicalData.name).toBeDefined();
      expect(unifiedEntity.sourceRecords).toHaveLength(recordsToUnify.length);

      // Verify records were updated
      for (const record of recordsToUnify) {
        const updatedRecord = await Record.findById(record._id);
        expect(updatedRecord.unifiedEntityId).toBe(unifiedEntity.unifiedId);
        expect(updatedRecord.processingStatus).toBe('unified');
      }
    }

    // Final verification
    const finalRecords = await Record.find({ source: 'test-integration' });
    const unifiedEntities = await UnifiedEntity.find({ entityType: 'pharmacy' });
    
    expect(finalRecords.length).toBeGreaterThan(0);
    expect(unifiedEntities.length).toBeGreaterThan(0);
  });

  test('should handle Arabic text normalization correctly', async () => {
    const arabicData = {
      name: 'صيدلية الشفاء الطبية',
      address: 'شارع التحرير، القاهرة',
      phone: '**********'
    };

    const record = await dataIngestionService.ingestRecord(arabicData, {
      source: 'test-arabic',
      entityType: 'pharmacy'
    });

    const normalizedData = await preprocessingService.normalizeRecord(record.originalData, 'pharmacy');
    
    expect(normalizedData.name).toBeDefined();
    expect(normalizedData.transliterations.nameEnglish).toBeDefined();
    expect(normalizedData.languages.name).toBe('ar');
  });

  test('should handle English text normalization correctly', async () => {
    const englishData = {
      name: 'Al-Shifa Medical Pharmacy',
      address: 'Tahrir Street, Cairo',
      phone: '**********'
    };

    const record = await dataIngestionService.ingestRecord(englishData, {
      source: 'test-english',
      entityType: 'pharmacy'
    });

    const normalizedData = await preprocessingService.normalizeRecord(record.originalData, 'pharmacy');
    
    expect(normalizedData.name).toBeDefined();
    expect(normalizedData.transliterations.nameArabic).toBeDefined();
    expect(normalizedData.languages.name).toBe('en');
  });

  test('should detect and handle duplicate records', async () => {
    const duplicateData = [
      {
        id: 'DUPLICATE_TEST_1',
        name: 'Test Pharmacy',
        address: 'Test Address',
        phone: '**********'
      },
      {
        id: 'DUPLICATE_TEST_1', // Same ID
        name: 'Test Pharmacy',
        address: 'Test Address',
        phone: '**********'
      }
    ];

    const results = await dataIngestionService.ingestFromJSON(duplicateData, {
      source: 'test-duplicates',
      entityType: 'pharmacy',
      skipDuplicates: true
    });

    expect(results.recordCount).toBe(1);
    expect(results.skipped).toBe(1);
  });

  test('should handle similarity matching with high confidence', async () => {
    // Create two very similar records
    const record1Data = {
      name: 'Cairo Medical Pharmacy',
      address: 'Downtown Cairo',
      phone: '**********'
    };

    const record2Data = {
      name: 'Cairo Medical Pharmacy',
      address: 'Downtown Cairo',
      phone: '**********'
    };

    const record1 = await dataIngestionService.ingestRecord(record1Data, {
      source: 'test-similarity-1',
      entityType: 'pharmacy'
    });

    const record2 = await dataIngestionService.ingestRecord(record2Data, {
      source: 'test-similarity-2',
      entityType: 'pharmacy'
    });

    // Normalize both records
    record1.normalizedData = await preprocessingService.normalizeRecord(record1.originalData, 'pharmacy');
    record2.normalizedData = await preprocessingService.normalizeRecord(record2.originalData, 'pharmacy');
    
    await record1.save();
    await record2.save();

    // Find matches
    const matches = await similarityService.findMatches(record1, 'pharmacy', 0.8);
    
    expect(matches).toHaveLength(1);
    expect(matches[0].recordId.toString()).toBe(record2._id.toString());
    expect(matches[0].confidence).toBeGreaterThan(0.9);
  });

  test('should create audit trail entries', async () => {
    const testData = {
      name: 'Audit Test Pharmacy',
      address: 'Test Address',
      phone: '**********'
    };

    const record = await dataIngestionService.ingestRecord(testData, {
      source: 'test-audit',
      entityType: 'pharmacy'
    });

    // Add audit entry
    record.addAuditEntry('test_action', 'test-user', { test: 'data' });
    await record.save();

    const updatedRecord = await Record.findById(record._id);
    expect(updatedRecord.auditTrail).toHaveLength(1);
    expect(updatedRecord.auditTrail[0].action).toBe('test_action');
    expect(updatedRecord.auditTrail[0].userId).toBe('test-user');
  });

  test('should handle processing status updates', async () => {
    const testData = {
      name: 'Status Test Pharmacy',
      address: 'Test Address'
    };

    const record = await dataIngestionService.ingestRecord(testData, {
      source: 'test-status',
      entityType: 'pharmacy'
    });

    expect(record.processingStatus).toBe('pending');

    record.updateProcessingStatus('normalized');
    await record.save();

    const updatedRecord = await Record.findById(record._id);
    expect(updatedRecord.processingStatus).toBe('normalized');
  });
});
