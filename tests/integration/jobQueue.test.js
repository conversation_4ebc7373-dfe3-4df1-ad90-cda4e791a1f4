const { queueManager, JOB_TYPES, JOB_PRIORITY } = require('../../src/jobs/jobQueue');
const { setupJobs } = require('../../src/jobs/jobSetup');
const logger = require('../../src/utils/logger');

// Mock <PERSON>is for testing
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    connect: jest.fn().mockResolvedValue(true),
    disconnect: jest.fn().mockResolvedValue(true),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    flushall: jest.fn()
  }));
});

// Mock Bull queue
jest.mock('bull', () => {
  const mockJob = {
    id: 'test-job-123',
    name: 'test-job',
    data: { test: 'data' },
    progress: jest.fn(),
    getState: jest.fn().mockResolvedValue('completed'),
    timestamp: Date.now(),
    processedOn: Date.now(),
    finishedOn: Date.now(),
    failedReason: null,
    attemptsMade: 1,
    opts: { attempts: 3 }
  };

  const mockQueue = {
    add: jest.fn().mockResolvedValue(mockJob),
    getJob: jest.fn().mockResolvedValue(mockJob),
    getWaiting: jest.fn().mockResolvedValue([]),
    getActive: jest.fn().mockResolvedValue([]),
    getCompleted: jest.fn().mockResolvedValue([]),
    getFailed: jest.fn().mockResolvedValue([]),
    getDelayed: jest.fn().mockResolvedValue([]),
    pause: jest.fn().mockResolvedValue(true),
    resume: jest.fn().mockResolvedValue(true),
    clean: jest.fn().mockResolvedValue(true),
    process: jest.fn(),
    on: jest.fn(),
    close: jest.fn().mockResolvedValue(true)
  };

  return jest.fn().mockImplementation(() => mockQueue);
});

describe('Job Queue Integration Tests', () => {
  beforeAll(async () => {
    // Mock the Redis initialization
    require('../../src/config/redis').initializeRedis = jest.fn().mockResolvedValue(true);
    require('../../src/config/redis').getRedis = jest.fn().mockReturnValue({
      on: jest.fn(),
      disconnect: jest.fn()
    });
  });

  afterAll(async () => {
    // Clean up any test jobs
    try {
      await queueManager.cleanQueue('dataIngestion', 0, 1000);
      await queueManager.cleanQueue('preprocessing', 0, 1000);
      await queueManager.cleanQueue('similarityMatching', 0, 1000);
      await queueManager.cleanQueue('entityUnification', 0, 1000);
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  test('should add job to data ingestion queue', async () => {
    const jobData = {
      filePath: '/test/path/file.csv',
      options: {
        source: 'test-source',
        entityType: 'pharmacy',
        skipDuplicates: true
      }
    };

    const job = await queueManager.addJob('dataIngestion', JOB_TYPES.INGEST_CSV, jobData, {
      priority: JOB_PRIORITY.HIGH
    });

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should add job to preprocessing queue', async () => {
    const jobData = {
      recordId: 'test-record-123',
      entityType: 'pharmacy'
    };

    const job = await queueManager.addJob('preprocessing', JOB_TYPES.NORMALIZE_RECORD, jobData);

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should add job to similarity matching queue', async () => {
    const jobData = {
      recordId: 'test-record-123',
      entityType: 'pharmacy',
      threshold: 0.8
    };

    const job = await queueManager.addJob('similarityMatching', JOB_TYPES.FIND_MATCHES, jobData);

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should add job to entity unification queue', async () => {
    const jobData = {
      recordId: 'test-record-123',
      matches: [
        { recordId: 'match-1', confidence: 0.95 },
        { recordId: 'match-2', confidence: 0.87 }
      ],
      entityType: 'pharmacy'
    };

    const job = await queueManager.addJob('entityUnification', JOB_TYPES.UNIFY_ENTITIES, jobData);

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should get job status', async () => {
    const status = await queueManager.getJobStatus('dataIngestion', 'test-job-123');

    expect(status).toBeDefined();
    expect(status.id).toBe('test-job-123');
    expect(status.state).toBe('completed');
  });

  test('should get queue statistics', async () => {
    const stats = await queueManager.getQueueStats('dataIngestion');

    expect(stats).toBeDefined();
    expect(stats.queueName).toBe('dataIngestion');
    expect(typeof stats.waiting).toBe('number');
    expect(typeof stats.active).toBe('number');
    expect(typeof stats.completed).toBe('number');
    expect(typeof stats.failed).toBe('number');
    expect(typeof stats.total).toBe('number');
  });

  test('should get all queue statistics', async () => {
    const allStats = await queueManager.getAllQueueStats();

    expect(allStats).toBeDefined();
    expect(allStats.dataIngestion).toBeDefined();
    expect(allStats.preprocessing).toBeDefined();
    expect(allStats.similarityMatching).toBeDefined();
    expect(allStats.entityUnification).toBeDefined();
  });

  test('should pause and resume queue', async () => {
    await queueManager.pauseQueue('dataIngestion');
    await queueManager.resumeQueue('dataIngestion');
    
    // Should complete without throwing
    expect(true).toBe(true);
  });

  test('should clean queue', async () => {
    await queueManager.cleanQueue('dataIngestion', 0, 10);
    
    // Should complete without throwing
    expect(true).toBe(true);
  });

  test('should handle invalid queue name', async () => {
    await expect(queueManager.addJob('invalidQueue', JOB_TYPES.INGEST_CSV, {}))
      .rejects.toThrow('Queue \'invalidQueue\' not found');
  });

  test('should handle job with different priorities', async () => {
    const lowPriorityJob = await queueManager.addJob('dataIngestion', JOB_TYPES.INGEST_CSV, {}, {
      priority: JOB_PRIORITY.LOW
    });

    const highPriorityJob = await queueManager.addJob('dataIngestion', JOB_TYPES.INGEST_CSV, {}, {
      priority: JOB_PRIORITY.HIGH
    });

    expect(lowPriorityJob).toBeDefined();
    expect(highPriorityJob).toBeDefined();
  });

  test('should handle batch processing job', async () => {
    const jobData = {
      source: 'test-source',
      entityType: 'pharmacy',
      batchSize: 50
    };

    const job = await queueManager.addJob('batchProcessing', JOB_TYPES.PROCESS_DATASET, jobData);

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should handle cleanup jobs', async () => {
    const cleanupData = {
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    };

    const job = await queueManager.addJob('cleanup', JOB_TYPES.CLEANUP_TEMP_FILES, cleanupData);

    expect(job).toBeDefined();
    expect(job.id).toBe('test-job-123');
  });

  test('should validate job types', () => {
    expect(JOB_TYPES.INGEST_CSV).toBe('ingest-csv');
    expect(JOB_TYPES.NORMALIZE_RECORD).toBe('normalize-record');
    expect(JOB_TYPES.FIND_MATCHES).toBe('find-matches');
    expect(JOB_TYPES.UNIFY_ENTITIES).toBe('unify-entities');
    expect(JOB_TYPES.PROCESS_DATASET).toBe('process-dataset');
    expect(JOB_TYPES.CLEANUP_TEMP_FILES).toBe('cleanup-temp-files');
  });

  test('should validate job priorities', () => {
    expect(JOB_PRIORITY.LOW).toBe(1);
    expect(JOB_PRIORITY.NORMAL).toBe(5);
    expect(JOB_PRIORITY.HIGH).toBe(10);
    expect(JOB_PRIORITY.CRITICAL).toBe(15);
  });
});
