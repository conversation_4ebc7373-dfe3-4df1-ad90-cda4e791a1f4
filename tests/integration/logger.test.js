const fs = require('fs');
const path = require('path');
const logger = require('../../src/utils/logger');

describe('Logger Integration Tests', () => {
  const testLogsDir = path.join(process.cwd(), 'logs');
  
  beforeAll(() => {
    // Ensure logs directory exists
    if (!fs.existsSync(testLogsDir)) {
      fs.mkdirSync(testLogsDir, { recursive: true });
    }
  });

  afterAll(() => {
    // Clean up test logs
    try {
      const files = fs.readdirSync(testLogsDir);
      files.forEach(file => {
        if (file.includes('test') || file.includes('error') || file.includes('combined')) {
          fs.unlinkSync(path.join(testLogsDir, file));
        }
      });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  test('should create log files on first write', () => {
    logger.info('Test log message');
    
    // Check if log files are created
    expect(fs.existsSync(path.join(testLogsDir, 'combined.log'))).toBe(true);
  });

  test('should log different levels correctly', () => {
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');
    
    // All should complete without throwing
    expect(true).toBe(true);
  });

  test('should handle structured logging with metadata', () => {
    const metadata = {
      userId: 'test-user',
      action: 'test-action',
      timestamp: new Date().toISOString()
    };
    
    logger.info('Structured log test', metadata);
    expect(true).toBe(true);
  });

  test('should support audit logging for data ingestion', () => {
    const auditData = {
      recordId: 'test-record-123',
      source: 'test-source',
      entityType: 'pharmacy',
      recordCount: 5
    };
    
    logger.audit.dataIngestion('TEST_INGESTION', auditData);
    expect(true).toBe(true);
  });

  test('should support audit logging for preprocessing', () => {
    const auditData = {
      recordId: 'test-record-123',
      originalData: { name: 'Test Pharmacy' },
      normalizedData: { name: 'test pharmacy' },
      processingTime: 150
    };
    
    logger.audit.preprocessing('TEST_PREPROCESSING', auditData);
    expect(true).toBe(true);
  });

  test('should support audit logging for similarity matching', () => {
    const auditData = {
      recordId: 'test-record-123',
      candidateMatches: 3,
      matchScores: [0.85, 0.72, 0.68],
      threshold: 0.7,
      processingTime: 250
    };
    
    logger.audit.similarityMatching('TEST_MATCHING', auditData);
    expect(true).toBe(true);
  });

  test('should support audit logging for entity unification', () => {
    const auditData = {
      unifiedEntityId: 'unified-123',
      sourceRecords: ['record-1', 'record-2'],
      canonicalData: { name: 'Test Pharmacy' },
      confidence: 0.92,
      manualReview: false
    };
    
    logger.audit.entityUnification('TEST_UNIFICATION', auditData);
    expect(true).toBe(true);
  });

  test('should support audit logging for job processing', () => {
    const auditData = {
      jobId: 'job-123',
      jobType: 'test-job',
      status: 'completed',
      progress: 100,
      processingTime: 5000
    };
    
    logger.audit.jobProcessing('TEST_JOB', auditData);
    expect(true).toBe(true);
  });

  test('should support correlation ID logging', () => {
    const correlationId = 'test-correlation-123';
    const correlatedLogger = logger.withCorrelationId(correlationId);
    
    correlatedLogger.info('Test message with correlation ID');
    correlatedLogger.error('Test error with correlation ID');
    
    expect(true).toBe(true);
  });

  test('should handle performance logging', () => {
    const performanceData = {
      operation: 'test-operation',
      duration: 1500,
      recordsProcessed: 100
    };
    
    logger.audit.performance('TEST_PERFORMANCE', performanceData);
    expect(true).toBe(true);
  });

  test('should handle errors gracefully', () => {
    const error = new Error('Test error');
    error.stack = 'Test stack trace';
    
    logger.error('Test error logging', {
      error: error.message,
      stack: error.stack,
      context: 'integration-test'
    });
    
    expect(true).toBe(true);
  });

  test('should write to audit log file', (done) => {
    logger.audit.dataIngestion('AUDIT_FILE_TEST', {
      recordId: 'audit-test-123',
      source: 'test',
      entityType: 'pharmacy',
      recordCount: 1
    });
    
    // Give some time for file write
    setTimeout(() => {
      expect(fs.existsSync(path.join(testLogsDir, 'audit.log'))).toBe(true);
      done();
    }, 100);
  });
});
