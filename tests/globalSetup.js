const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('Setting up global test environment...');
  
  // Create test directories
  const testDirs = [
    path.join(process.cwd(), 'logs'),
    path.join(process.cwd(), 'uploads'),
    path.join(process.cwd(), 'temp'),
    path.join(process.cwd(), 'coverage')
  ];

  for (const dir of testDirs) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created test directory: ${dir}`);
    }
  }

  // Set global test environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error';
  process.env.MONGODB_URI = 'mongodb://localhost:27017/data_unification_test';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';

  console.log('Global test setup completed');
};
