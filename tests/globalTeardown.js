const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('Cleaning up global test environment...');
  
  // Clean up test files
  const testDirs = [
    path.join(process.cwd(), 'logs'),
    path.join(process.cwd(), 'uploads'),
    path.join(process.cwd(), 'temp')
  ];

  for (const dir of testDirs) {
    try {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        for (const file of files) {
          if (file.includes('test') || file.includes('temp')) {
            const filePath = path.join(dir, file);
            fs.unlinkSync(filePath);
          }
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not clean up directory ${dir}:`, error.message);
    }
  }

  console.log('Global test cleanup completed');
};
