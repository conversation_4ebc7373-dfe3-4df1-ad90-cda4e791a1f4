// Global test setup
const path = require('path');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
process.env.MONGODB_URI = 'mongodb://localhost:27017/data_unification_test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

// Mock console methods to reduce test output noise
const originalConsole = { ...console };

beforeAll(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole);
});

// Global test utilities
global.testUtils = {
  // Helper to create test data
  createTestRecord: (overrides = {}) => ({
    sourceId: 'test-record-' + Date.now(),
    source: 'test-source',
    entityType: 'pharmacy',
    originalData: {
      name: 'Test Pharmacy',
      address: 'Test Address',
      phone: '1234567890',
      ...overrides.originalData
    },
    processingStatus: 'pending',
    autoProcess: true,
    ...overrides
  }),

  // Helper to create test unified entity
  createTestUnifiedEntity: (overrides = {}) => ({
    unifiedId: 'test-unified-' + Date.now(),
    entityType: 'pharmacy',
    canonicalData: {
      name: 'Test Pharmacy',
      address: 'Test Address',
      phone: '1234567890',
      ...overrides.canonicalData
    },
    aliases: [],
    sourceRecords: [],
    statistics: {
      totalRecords: 1,
      averageConfidence: 1.0,
      lastUpdated: new Date()
    },
    reviewStatus: 'auto_unified',
    requiresManualReview: false,
    ...overrides
  }),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to generate random test data
  generateRandomString: (length = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Helper to create mock job
  createMockJob: (data = {}, options = {}) => ({
    id: 'test-job-' + Date.now(),
    name: 'test-job',
    data,
    progress: jest.fn(),
    getState: jest.fn().mockResolvedValue('completed'),
    timestamp: Date.now(),
    processedOn: Date.now(),
    finishedOn: Date.now(),
    failedReason: null,
    attemptsMade: 1,
    opts: { attempts: 3, ...options }
  })
};

// Mock external dependencies that might not be available in test environment
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    connect: jest.fn().mockResolvedValue(true),
    disconnect: jest.fn().mockResolvedValue(true),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    flushall: jest.fn()
  }));
});

// Mock Bull queue for job testing
jest.mock('bull', () => {
  const mockQueue = {
    add: jest.fn().mockResolvedValue(global.testUtils?.createMockJob() || { id: 'mock-job' }),
    getJob: jest.fn().mockResolvedValue(global.testUtils?.createMockJob() || { id: 'mock-job' }),
    getWaiting: jest.fn().mockResolvedValue([]),
    getActive: jest.fn().mockResolvedValue([]),
    getCompleted: jest.fn().mockResolvedValue([]),
    getFailed: jest.fn().mockResolvedValue([]),
    getDelayed: jest.fn().mockResolvedValue([]),
    pause: jest.fn().mockResolvedValue(true),
    resume: jest.fn().mockResolvedValue(true),
    clean: jest.fn().mockResolvedValue(true),
    process: jest.fn(),
    on: jest.fn(),
    close: jest.fn().mockResolvedValue(true)
  };

  return jest.fn().mockImplementation(() => mockQueue);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
