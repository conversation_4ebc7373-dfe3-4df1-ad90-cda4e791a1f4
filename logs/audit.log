{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:07:59.247","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.252","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:07:59.253","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:07:59.254","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:07:59.255","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.256","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.257","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":202934,"user":1410397},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1737666,"external":2413104,"heapTotal":74838016,"heapUsed":49475808,"rss":123723776},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.258","uptime":1.066039321,"version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:07:59.260","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.481","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.487","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.488","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.490","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.527","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.527","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.528","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.529","version":"1.0.0"}
