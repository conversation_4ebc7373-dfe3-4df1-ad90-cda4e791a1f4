{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:07:59.247","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.252","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:07:59.253","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:07:59.254","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:07:59.255","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.256","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.257","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":202934,"user":1410397},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1737666,"external":2413104,"heapTotal":74838016,"heapUsed":49475808,"rss":123723776},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:07:59.258","uptime":1.066039321,"version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:07:59.260","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.481","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.487","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.488","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.490","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.527","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.527","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.528","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:08:00.529","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:28:05.622","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:28:05.633","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:28:05.634","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:28:05.635","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:28:05.636","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:28:05.637","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:28:05.638","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":132794,"user":680069},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1755671,"external":2431109,"heapTotal":55975936,"heapUsed":28718296,"rss":94826496},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:28:05.639","uptime":0.779096399,"version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:28:05.642","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.712","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.717","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.718","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.719","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.774","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.775","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.775","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:28:06.776","version":"1.0.0"}
{"level":"info","message":"Test log message","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.209","version":"1.0.0"}
{"level":"info","message":"Info message","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.214","version":"1.0.0"}
{"level":"warn","message":"Warning message","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.215","version":"1.0.0"}
{"level":"error","message":"Error message","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.215","version":"1.0.0"}
{"action":"test-action","level":"info","message":"Structured log test","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.219","userId":"test-user","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:32:26.221","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.222","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:32:26.223","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:32:26.225","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:32:26.226","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.227","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.227","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":201812,"user":980808},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1662698,"external":2420814,"heapTotal":74051584,"heapUsed":44171400,"rss":122851328},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:32:26.229","uptime":0.911085498,"version":"1.0.0"}
{"context":"integration-test","error":"Test error","level":"error","message":"Test error logging","service":"data-unification-system","stack":"Test stack trace","timestamp":"2025-06-29 11:32:26.231","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:32:26.234","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.082","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.086","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.087","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.088","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' paused","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.091","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' resumed","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.092","version":"1.0.0"}
{"grace":0,"level":"info","limit":10,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.093","version":"1.0.0"}
{"error":"Queue 'invalidQueue' not found","jobType":"ingest-csv","level":"error","message":"Failed to add job to queue","queueName":"invalidQueue","service":"data-unification-system","stack":"Error: Queue 'invalidQueue' not found\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/src/jobs/jobQueue.js:88:15)\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/tests/integration/jobQueue.test.js:180:31)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:32:27.110","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.111","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.112","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.113","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.113","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.117","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'preprocessing' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.118","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'similarityMatching' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.118","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'entityUnification' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:32:27.118","version":"1.0.0"}
{"level":"info","message":"Test log message","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.133","version":"1.0.0"}
{"level":"info","message":"Info message","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.138","version":"1.0.0"}
{"level":"warn","message":"Warning message","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.139","version":"1.0.0"}
{"level":"error","message":"Error message","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.139","version":"1.0.0"}
{"action":"test-action","level":"info","message":"Structured log test","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.141","userId":"test-user","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:34:43.143","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.144","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:34:43.145","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:34:43.147","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:34:43.148","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.149","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.150","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":110472,"user":617120},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1681808,"external":2357246,"heapTotal":55975936,"heapUsed":28329448,"rss":94879744},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.151","uptime":0.659705322,"version":"1.0.0"}
{"context":"integration-test","error":"Test error","level":"error","message":"Test error logging","service":"data-unification-system","stack":"Test stack trace","timestamp":"2025-06-29 11:34:43.152","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:34:43.154","version":"1.0.0"}
{"level":"error","message":"Error processing JSON record: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.processJSONBatch (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:199:24)\n    at DataIngestionService.processJSONBatch [as ingestFromJSON] (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:168:20)\n    at Object.ingestFromJSON (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:122:57)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.814","version":"1.0.0"}
{"level":"error","message":"Error processing JSON record: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.processJSONBatch (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:199:24)\n    at DataIngestionService.processJSONBatch [as ingestFromJSON] (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:168:20)\n    at Object.ingestFromJSON (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:122:57)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.819","version":"1.0.0"}
{"level":"error","message":"Error processing JSON record: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.processJSONBatch (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:199:24)\n    at DataIngestionService.processJSONBatch [as ingestFromJSON] (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:168:20)\n    at Object.ingestFromJSON (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:122:57)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.820","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":0,"recordId":null,"service":"data-unification-system","source":"test-integration","timestamp":"2025-06-29 11:34:43.820","version":"1.0.0"}
{"level":"error","message":"Real-time ingestion error: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.ingestRecord (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:237:22)\n    at Object.ingestRecord (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:217:47)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.825","version":"1.0.0"}
{"level":"error","message":"Real-time ingestion error: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.ingestRecord (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:237:22)\n    at Object.ingestRecord (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:236:47)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.854","version":"1.0.0"}
{"level":"error","message":"Error processing JSON record: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.processJSONBatch (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:199:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at DataIngestionService.ingestFromJSON (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:168:9)\n    at Object.<anonymous> (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:264:21)","timestamp":"2025-06-29 11:34:43.857","version":"1.0.0"}
{"level":"error","message":"Error processing JSON record: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.processJSONBatch (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:199:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at DataIngestionService.ingestFromJSON (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:168:9)\n    at Object.<anonymous> (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:264:21)","timestamp":"2025-06-29 11:34:43.859","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":0,"recordId":null,"service":"data-unification-system","source":"test-duplicates","timestamp":"2025-06-29 11:34:43.859","version":"1.0.0"}
{"level":"error","message":"Real-time ingestion error: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.ingestRecord (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:237:22)\n    at Object.ingestRecord (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:288:48)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.862","version":"1.0.0"}
{"level":"error","message":"Real-time ingestion error: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.ingestRecord (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:237:22)\n    at Object.ingestRecord (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:320:47)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.865","version":"1.0.0"}
{"level":"error","message":"Real-time ingestion error: Record is not a constructor","service":"data-unification-system","stack":"TypeError: Record is not a constructor\n    at DataIngestionService.ingestRecord (/home/<USER>/intelligent-data-unification-system/src/services/DataIngestionService.js:237:22)\n    at Object.ingestRecord (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:341:47)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:43.867","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.989","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.994","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.995","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.995","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' paused","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.999","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' resumed","service":"data-unification-system","timestamp":"2025-06-29 11:34:43.999","version":"1.0.0"}
{"grace":0,"level":"info","limit":10,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.001","version":"1.0.0"}
{"error":"Queue 'invalidQueue' not found","jobType":"ingest-csv","level":"error","message":"Failed to add job to queue","queueName":"invalidQueue","service":"data-unification-system","stack":"Error: Queue 'invalidQueue' not found\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/src/jobs/jobQueue.js:88:15)\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/tests/integration/jobQueue.test.js:180:31)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:34:44.018","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.020","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.020","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.023","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.024","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.027","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'preprocessing' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.028","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'similarityMatching' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.028","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'entityUnification' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:34:44.029","version":"1.0.0"}
{"level":"info","message":"Test log message","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.079","version":"1.0.0"}
{"level":"info","message":"Info message","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.084","version":"1.0.0"}
{"level":"warn","message":"Warning message","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.085","version":"1.0.0"}
{"level":"error","message":"Error message","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.085","version":"1.0.0"}
{"action":"test-action","level":"info","message":"Structured log test","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.086","userId":"test-user","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:35:56.087","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.088","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:35:56.089","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:35:56.090","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:35:56.091","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.092","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.093","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":73681,"user":597637},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1694726,"external":2370164,"heapTotal":55713792,"heapUsed":28300432,"rss":95166464},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.094","uptime":0.614458797,"version":"1.0.0"}
{"context":"integration-test","error":"Test error","level":"error","message":"Test error logging","service":"data-unification-system","stack":"Test stack trace","timestamp":"2025-06-29 11:35:56.095","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:35:56.096","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":3,"recordId":null,"service":"data-unification-system","source":"test-integration","timestamp":"2025-06-29 11:35:56.779","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-arabic","timestamp":"2025-06-29 11:35:56.808","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"شارع التحرىر، القاهره","addressTokens":["التحرىر،","القاهره"],"entityType":"pharmacy","languages":{"address":"ar","name":"ar"},"name":"صىدلىه الشفاء الطبىه","nameTokens":["صىدلىه","الشفاء","الطبىه"],"phone":"**********","transliterations":{"addressArabic":"شارع التحرىر، القاهره","addressEnglish":"shraa ltHryr, lqhr@","nameArabic":"صىدلىه الشفاء الطبىه","nameEnglish":"Sydly@ lshf lTby@"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"شارع التحرير، القاهرة","name":"صيدلية الشفاء الطبية","phone":"**********"},"processingTime":2,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:35:56.811","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-english","timestamp":"2025-06-29 11:35:56.813","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"tahrir street cairo","addressTokens":["tahrir","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"al shifa medical pharmacy","nameTokens":["al","shifa","medic"],"phone":"**********","transliterations":{"addressArabic":"Tahrir Street, Cairo","addressEnglish":"Tahrir Street, Cairo","nameArabic":"Al-Shifa Medical Pharmacy","nameEnglish":"Al-Shifa Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Tahrir Street, Cairo","name":"Al-Shifa Medical Pharmacy","phone":"**********"},"processingTime":1,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:35:56.815","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":2,"recordId":null,"service":"data-unification-system","source":"test-duplicates","timestamp":"2025-06-29 11:35:56.817","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-similarity-1","timestamp":"2025-06-29 11:35:56.820","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-similarity-2","timestamp":"2025-06-29 11:35:56.820","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"downtown cairo","addressTokens":["downtown","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"cairo medical pharmacy","nameTokens":["cairo","medic"],"phone":"**********","transliterations":{"addressArabic":"Downtown Cairo","addressEnglish":"Downtown Cairo","nameArabic":"Cairo Medical Pharmacy","nameEnglish":"Cairo Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Downtown Cairo","name":"Cairo Medical Pharmacy","phone":"**********"},"processingTime":0,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:35:56.821","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"downtown cairo","addressTokens":["downtown","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"cairo medical pharmacy","nameTokens":["cairo","medic"],"phone":"**********","transliterations":{"addressArabic":"Downtown Cairo","addressEnglish":"Downtown Cairo","nameArabic":"Cairo Medical Pharmacy","nameEnglish":"Cairo Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Downtown Cairo","name":"Cairo Medical Pharmacy","phone":"**********"},"processingTime":0,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:35:56.822","version":"1.0.0"}
{"level":"error","message":"Find matches error: Record.find(...).limit is not a function","service":"data-unification-system","stack":"TypeError: Record.find(...).limit is not a function\n    at SimilarityService.limit [as findMatches] (/home/<USER>/intelligent-data-unification-system/src/services/SimilarityService.js:185:10)\n    at Object.findMatches (/home/<USER>/intelligent-data-unification-system/tests/integration/dataFlow.test.js:320:45)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-29 11:35:56.831","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-audit","timestamp":"2025-06-29 11:35:56.833","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id","service":"data-unification-system","source":"test-status","timestamp":"2025-06-29 11:35:56.835","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.985","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.990","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.991","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.992","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' paused","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.995","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' resumed","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.996","version":"1.0.0"}
{"grace":0,"level":"info","limit":10,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:35:56.997","version":"1.0.0"}
{"error":"Queue 'invalidQueue' not found","jobType":"ingest-csv","level":"error","message":"Failed to add job to queue","queueName":"invalidQueue","service":"data-unification-system","stack":"Error: Queue 'invalidQueue' not found\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/src/jobs/jobQueue.js:88:15)\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/tests/integration/jobQueue.test.js:180:31)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:35:57.013","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.015","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.015","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.016","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.017","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.019","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'preprocessing' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.019","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'similarityMatching' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.020","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'entityUnification' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:35:57.020","version":"1.0.0"}
{"level":"info","message":"Test log message","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.432","version":"1.0.0"}
{"level":"info","message":"Info message","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.437","version":"1.0.0"}
{"level":"warn","message":"Warning message","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.438","version":"1.0.0"}
{"level":"error","message":"Error message","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.439","version":"1.0.0"}
{"action":"test-action","level":"info","message":"Structured log test","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.440","userId":"test-user","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"TEST_INGESTION","recordCount":5,"recordId":"test-record-123","service":"data-unification-system","source":"test-source","timestamp":"2025-06-29 11:38:37.441","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"name":"test pharmacy"},"operation":"TEST_PREPROCESSING","originalData":{"name":"Test Pharmacy"},"processingTime":150,"recordId":"test-record-123","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.442","version":"1.0.0"}
{"candidateMatches":3,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[0.85,0.72,0.68],"message":"Similarity matching operation","operation":"TEST_MATCHING","processingTime":250,"recordId":"test-record-123","service":"data-unification-system","threshold":0.7,"timestamp":"2025-06-29 11:38:37.443","version":"1.0.0"}
{"canonicalData":{"name":"Test Pharmacy"},"category":"ENTITY_UNIFICATION","confidence":0.92,"level":"info","manualReview":false,"message":"Entity unification operation","operation":"TEST_UNIFICATION","service":"data-unification-system","sourceRecords":["record-1","record-2"],"timestamp":"2025-06-29 11:38:37.445","unifiedEntityId":"unified-123","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"job-123","jobType":"test-job","level":"info","message":"Job processing operation","operation":"TEST_JOB","processingTime":5000,"progress":100,"service":"data-unification-system","status":"completed","timestamp":"2025-06-29 11:38:37.446","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"info","message":"Test message with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.447","version":"1.0.0"}
{"correlationId":"test-correlation-123","level":"error","message":"Test error with correlation ID","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.448","version":"1.0.0"}
{"category":"PERFORMANCE","cpuUsage":{"system":81012,"user":570943},"customMetrics":{"duration":1500,"operation":"test-operation","recordsProcessed":100},"level":"info","memoryUsage":{"arrayBuffers":1665839,"external":2341277,"heapTotal":55713792,"heapUsed":28237520,"rss":95154176},"message":"Performance metrics","operation":"TEST_PERFORMANCE","service":"data-unification-system","timestamp":"2025-06-29 11:38:37.449","uptime":0.604866473,"version":"1.0.0"}
{"context":"integration-test","error":"Test error","level":"error","message":"Test error logging","service":"data-unification-system","stack":"Test stack trace","timestamp":"2025-06-29 11:38:37.453","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"AUDIT_FILE_TEST","recordCount":1,"recordId":"audit-test-123","service":"data-unification-system","source":"test","timestamp":"2025-06-29 11:38:37.455","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":3,"recordId":null,"service":"data-unification-system","source":"test-integration","timestamp":"2025-06-29 11:38:38.176","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318198-0.3467458500555456","service":"data-unification-system","source":"test-arabic","timestamp":"2025-06-29 11:38:38.198","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"شارع التحرىر، القاهره","addressTokens":["التحرىر،","القاهره"],"entityType":"pharmacy","languages":{"address":"ar","name":"ar"},"name":"صىدلىه الشفاء الطبىه","nameTokens":["صىدلىه","الشفاء","الطبىه"],"phone":"**********","transliterations":{"addressArabic":"شارع التحرىر، القاهره","addressEnglish":"shraa ltHryr, lqhr@","nameArabic":"صىدلىه الشفاء الطبىه","nameEnglish":"Sydly@ lshf lTby@"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"شارع التحرير، القاهرة","name":"صيدلية الشفاء الطبية","phone":"**********"},"processingTime":4,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:38:38.203","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318205-0.4775605503135456","service":"data-unification-system","source":"test-english","timestamp":"2025-06-29 11:38:38.206","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"tahrir street cairo","addressTokens":["tahrir","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"al shifa medical pharmacy","nameTokens":["al","shifa","medic"],"phone":"**********","transliterations":{"addressArabic":"Tahrir Street, Cairo","addressEnglish":"Tahrir Street, Cairo","nameArabic":"Al-Shifa Medical Pharmacy","nameEnglish":"Al-Shifa Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Tahrir Street, Cairo","name":"Al-Shifa Medical Pharmacy","phone":"**********"},"processingTime":2,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:38:38.208","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"JSON_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":null,"service":"data-unification-system","source":"test-duplicates","timestamp":"2025-06-29 11:38:38.210","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318212-0.9432895935322552","service":"data-unification-system","source":"test-similarity-1","timestamp":"2025-06-29 11:38:38.212","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318212-0.5608039296377167","service":"data-unification-system","source":"test-similarity-2","timestamp":"2025-06-29 11:38:38.212","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"downtown cairo","addressTokens":["downtown","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"cairo medical pharmacy","nameTokens":["cairo","medic"],"phone":"**********","transliterations":{"addressArabic":"Downtown Cairo","addressEnglish":"Downtown Cairo","nameArabic":"Cairo Medical Pharmacy","nameEnglish":"Cairo Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Downtown Cairo","name":"Cairo Medical Pharmacy","phone":"**********"},"processingTime":0,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:38:38.213","version":"1.0.0"}
{"category":"PREPROCESSING","level":"info","message":"Preprocessing operation","normalizedData":{"address":"downtown cairo","addressTokens":["downtown","cairo"],"entityType":"pharmacy","languages":{"address":"en","name":"en"},"name":"cairo medical pharmacy","nameTokens":["cairo","medic"],"phone":"**********","transliterations":{"addressArabic":"Downtown Cairo","addressEnglish":"Downtown Cairo","nameArabic":"Cairo Medical Pharmacy","nameEnglish":"Cairo Medical Pharmacy"}},"operation":"RECORD_NORMALIZATION_SERVICE_COMPLETED","originalData":{"address":"Downtown Cairo","name":"Cairo Medical Pharmacy","phone":"**********"},"processingTime":1,"recordId":null,"service":"data-unification-system","timestamp":"2025-06-29 11:38:38.214","version":"1.0.0"}
{"candidateMatches":0,"category":"SIMILARITY_MATCHING","level":"info","matchScores":[],"message":"Similarity matching operation","operation":"FIND_MATCHES_SERVICE_COMPLETED","processingTime":0,"recordId":"mock-record-id-1751186318212-0.9432895935322552","service":"data-unification-system","threshold":0.8,"timestamp":"2025-06-29 11:38:38.215","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318216-0.8555190143683968","service":"data-unification-system","source":"test-audit","timestamp":"2025-06-29 11:38:38.217","version":"1.0.0"}
{"category":"DATA_INGESTION","entityType":"pharmacy","level":"info","message":"Data ingestion operation","operation":"SINGLE_RECORD_INGESTION_SERVICE_COMPLETED","recordCount":1,"recordId":"mock-record-id-1751186318218-0.29474474485298874","service":"data-unification-system","source":"test-status","timestamp":"2025-06-29 11:38:38.218","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.376","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"normalize-record","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.381","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"find-matches","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.382","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"unify-entities","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.383","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' paused","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.388","version":"1.0.0"}
{"level":"info","message":"Queue 'dataIngestion' resumed","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.389","version":"1.0.0"}
{"grace":0,"level":"info","limit":10,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.391","version":"1.0.0"}
{"error":"Queue 'invalidQueue' not found","jobType":"ingest-csv","level":"error","message":"Failed to add job to queue","queueName":"invalidQueue","service":"data-unification-system","stack":"Error: Queue 'invalidQueue' not found\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/src/jobs/jobQueue.js:88:15)\n    at Object.addJob (/home/<USER>/intelligent-data-unification-system/tests/integration/jobQueue.test.js:180:31)\n    at Promise.then.completed (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/intelligent-data-unification-system/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/intelligent-data-unification-system/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-06-29 11:38:38.409","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.412","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"ingest-csv","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.412","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"process-dataset","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.413","version":"1.0.0"}
{"category":"JOB_PROCESSING","jobId":"test-job-123","jobType":"cleanup-temp-files","level":"info","message":"Job processing operation","operation":"JOB_ADDED","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.414","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'dataIngestion' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.416","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'preprocessing' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.416","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'similarityMatching' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.416","version":"1.0.0"}
{"grace":0,"level":"info","limit":1000,"message":"Queue 'entityUnification' cleaned","service":"data-unification-system","timestamp":"2025-06-29 11:38:38.417","version":"1.0.0"}
